'use client'

import { useRef, useEffect, useState } from 'react'

export default function SubtitleDiagnosisPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [diagnostics, setDiagnostics] = useState<any>({})
  const [currentTime, setCurrentTime] = useState(0)

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const runDiagnostics = () => {
      const tracks = Array.from(video.textTracks)
      const videoRect = video.getBoundingClientRect()
      
      const diag = {
        videoElement: {
          src: video.src,
          readyState: video.readyState,
          currentTime: video.currentTime,
          duration: video.duration,
          videoWidth: video.videoWidth,
          videoHeight: video.videoHeight,
          clientWidth: video.clientWidth,
          clientHeight: video.clientHeight,
          crossOrigin: video.crossOrigin
        },
        textTracks: tracks.map((track, index) => ({
          index,
          kind: track.kind,
          label: track.label,
          language: track.language,
          mode: track.mode,
          cues: track.cues ? Array.from(track.cues).map(cue => ({
            startTime: cue.startTime,
            endTime: cue.endTime,
            text: cue.text,
            id: cue.id
          })) : [],
          activeCues: track.activeCues ? Array.from(track.activeCues).map(cue => ({
            startTime: cue.startTime,
            endTime: cue.endTime,
            text: cue.text
          })) : []
        })),
        browser: {
          userAgent: navigator.userAgent,
          platform: navigator.platform,
          language: navigator.language
        },
        styles: {
          computedStyle: window.getComputedStyle(video),
          videoRect: {
            width: videoRect.width,
            height: videoRect.height,
            top: videoRect.top,
            left: videoRect.left
          }
        }
      }
      
      setDiagnostics(diag)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
      runDiagnostics()
    }

    video.addEventListener('loadedmetadata', runDiagnostics)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('loadstart', runDiagnostics)
    video.addEventListener('canplay', runDiagnostics)

    // 强制启用所有字幕轨道
    const enableAllTracks = () => {
      const tracks = video.textTracks
      for (let i = 0; i < tracks.length; i++) {
        tracks[i].mode = 'showing'
        console.log(`启用轨道 ${i}:`, tracks[i])
      }
      runDiagnostics()
    }

    setTimeout(enableAllTracks, 2000)

    return () => {
      video.removeEventListener('loadedmetadata', runDiagnostics)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('loadstart', runDiagnostics)
      video.removeEventListener('canplay', runDiagnostics)
    }
  }, [])

  const testSubtitleFile = async (url: string) => {
    try {
      const response = await fetch(url)
      const text = await response.text()
      console.log('字幕文件内容:', text)
      return { success: true, content: text.substring(0, 500) }
    } catch (error) {
      console.error('字幕文件加载失败:', error)
      return { success: false, error: String(error) }
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">字幕问题诊断页面</h1>
        
        <div className="space-y-6">
          {/* 视频播放器 */}
          <div className="bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-auto"
              controls
              crossOrigin="anonymous"
              autoPlay={false}
            >
              <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
              <track
                kind="subtitles"
                src="/api/subtitles/test_simple.vtt"
                srcLang="zh"
                label="测试字幕"
                default
              />
            </video>
          </div>

          {/* 控制按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => testSubtitleFile('/api/subtitles/test_simple.vtt')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              测试字幕文件
            </button>
            <button
              onClick={() => {
                const video = videoRef.current
                if (video) {
                  for (let i = 0; i < video.textTracks.length; i++) {
                    video.textTracks[i].mode = 'showing'
                  }
                }
              }}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              强制启用字幕
            </button>
          </div>

          {/* 诊断信息 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 视频信息 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">视频元素信息</h2>
              {diagnostics.videoElement && (
                <div className="text-sm text-gray-300 space-y-1">
                  <p>源文件: {diagnostics.videoElement.src}</p>
                  <p>就绪状态: {diagnostics.videoElement.readyState}</p>
                  <p>当前时间: {diagnostics.videoElement.currentTime?.toFixed(2)}s</p>
                  <p>总时长: {diagnostics.videoElement.duration?.toFixed(2)}s</p>
                  <p>视频尺寸: {diagnostics.videoElement.videoWidth} x {diagnostics.videoElement.videoHeight}</p>
                  <p>显示尺寸: {diagnostics.videoElement.clientWidth} x {diagnostics.videoElement.clientHeight}</p>
                  <p>跨域设置: {diagnostics.videoElement.crossOrigin || '无'}</p>
                </div>
              )}
            </div>

            {/* 字幕轨道信息 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">字幕轨道信息</h2>
              {diagnostics.textTracks && diagnostics.textTracks.length > 0 ? (
                <div className="space-y-4">
                  {diagnostics.textTracks.map((track: any) => (
                    <div key={track.index} className="bg-gray-700 rounded p-3">
                      <h3 className="font-medium text-white">轨道 {track.index}: {track.label}</h3>
                      <div className="text-xs text-gray-300 mt-2 space-y-1">
                        <p>类型: {track.kind}</p>
                        <p>语言: {track.language}</p>
                        <p>模式: <span className={track.mode === 'showing' ? 'text-green-400' : 'text-red-400'}>{track.mode}</span></p>
                        <p>字幕条目数: {track.cues.length}</p>
                        <p>活跃字幕数: {track.activeCues.length}</p>
                        
                        {track.activeCues.length > 0 && (
                          <div className="mt-2 p-2 bg-gray-600 rounded">
                            <p className="text-yellow-400 font-medium">当前活跃字幕:</p>
                            {track.activeCues.map((cue: any, index: number) => (
                              <p key={index} className="text-white whitespace-pre-line">{cue.text}</p>
                            ))}
                          </div>
                        )}
                        
                        {track.cues.length > 0 && (
                          <details className="mt-2">
                            <summary className="text-blue-400 cursor-pointer">查看所有字幕条目</summary>
                            <div className="mt-2 max-h-40 overflow-y-auto">
                              {track.cues.map((cue: any, index: number) => (
                                <div key={index} className="text-xs p-1 border-b border-gray-600">
                                  <p>{cue.startTime.toFixed(2)}s - {cue.endTime.toFixed(2)}s</p>
                                  <p className="text-gray-200">{cue.text}</p>
                                </div>
                              ))}
                            </div>
                          </details>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-400">没有检测到字幕轨道</p>
              )}
            </div>

            {/* 浏览器信息 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">浏览器信息</h2>
              {diagnostics.browser && (
                <div className="text-sm text-gray-300 space-y-1">
                  <p>用户代理: {diagnostics.browser.userAgent}</p>
                  <p>平台: {diagnostics.browser.platform}</p>
                  <p>语言: {diagnostics.browser.language}</p>
                </div>
              )}
            </div>

            {/* 样式信息 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">样式信息</h2>
              {diagnostics.styles && (
                <div className="text-sm text-gray-300 space-y-1">
                  <p>视频位置: {diagnostics.styles.videoRect?.left}, {diagnostics.styles.videoRect?.top}</p>
                  <p>视频大小: {diagnostics.styles.videoRect?.width} x {diagnostics.styles.videoRect?.height}</p>
                </div>
              )}
            </div>
          </div>

          {/* 说明 */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">诊断说明</h2>
            <div className="text-gray-300 text-sm space-y-2">
              <p>1. 检查视频元素是否正确加载</p>
              <p>2. 检查字幕轨道是否正确添加和启用</p>
              <p>3. 检查字幕条目是否正确解析</p>
              <p>4. 检查当前时间是否有活跃的字幕</p>
              <p>5. 如果有活跃字幕但视频上不显示，可能是CSS样式问题</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
