'use client'

import { useState, useRef, useEffect } from 'react'

export default function FinalSubtitleTestPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [selectedSubtitle, setSelectedSubtitle] = useState('zh-ko')
  const [isPlaying, setIsPlaying] = useState(false)
  const [showCustomSubtitle, setShowCustomSubtitle] = useState(false)
  const [currentSubtitleText, setCurrentSubtitleText] = useState('')

  // 字幕数据
  const subtitleData = {
    'zh-ko': [
      { start: 0, end: 5, text: '欢迎观看这部精彩的电影\n이 멋진 영화를 시청해 주셔서 환영합니다' },
      { start: 5, end: 10, text: '这是中韩双语字幕演示\n이것은 중국어-한국어 이중 자막 데모입니다' },
      { start: 10, end: 15, text: '字幕会根据实际内容自动调整\n자막은 실제 내용에 따라 자동으로 조정됩니다' },
      { start: 15, end: 20, text: '祝您观影愉快！\n즐거운 시청 되세요!' },
      { start: 20, end: 25, text: '支持多种语言字幕选择\n다양한 언어 자막 선택을 지원합니다' },
      { start: 25, end: 30, text: '感谢您的观看\n시청해 주셔서 감사합니다' }
    ],
    'zh': [
      { start: 0, end: 5, text: '欢迎观看这部精彩的电影' },
      { start: 5, end: 10, text: '这是中文字幕演示' },
      { start: 10, end: 15, text: '字幕会根据实际内容自动调整' },
      { start: 15, end: 20, text: '祝您观影愉快！' },
      { start: 20, end: 25, text: '支持多种语言字幕选择' },
      { start: 25, end: 30, text: '感谢您的观看' }
    ],
    'ko': [
      { start: 0, end: 5, text: '이 멋진 영화를 시청해 주셔서 환영합니다' },
      { start: 5, end: 10, text: '이것은 한국어 자막 데모입니다' },
      { start: 10, end: 15, text: '자막은 실제 내용에 따라 자동으로 조정됩니다' },
      { start: 15, end: 20, text: '즐거운 시청 되세요!' },
      { start: 20, end: 25, text: '다양한 언어 자막 선택을 지원합니다' },
      { start: 25, end: 30, text: '시청해 주셔서 감사합니다' }
    ]
  }

  const subtitleTracks = [
    { id: 'none', label: '无字幕' },
    { id: 'zh-ko', label: '中韩字幕 (中文+韩文)' },
    { id: 'zh', label: '中文字幕' },
    { id: 'ko', label: '한국어 (韩文)' }
  ]

  // 处理时间更新，显示自定义字幕
  const handleTimeUpdate = () => {
    const video = videoRef.current
    if (!video || !showCustomSubtitle) return

    const currentTime = video.currentTime
    const subtitles = subtitleData[selectedSubtitle as keyof typeof subtitleData] || []
    
    const currentSubtitle = subtitles.find(sub => 
      currentTime >= sub.start && currentTime <= sub.end
    )

    setCurrentSubtitleText(currentSubtitle ? currentSubtitle.text : '')
  }

  // 切换字幕方式
  const handleSubtitleModeChange = (useCustom: boolean) => {
    setShowCustomSubtitle(useCustom)
    if (!useCustom) {
      setCurrentSubtitleText('')
    }
  }

  // 切换字幕语言
  const handleSubtitleChange = (subtitleId: string) => {
    setSelectedSubtitle(subtitleId)
    if (subtitleId === 'none') {
      setCurrentSubtitleText('')
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    video.addEventListener('timeupdate', handleTimeUpdate)
    return () => video.removeEventListener('timeupdate', handleTimeUpdate)
  }, [selectedSubtitle, showCustomSubtitle])

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">最终字幕解决方案测试</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 视频播放器 */}
          <div className="lg:col-span-2 space-y-4">
            <div className="bg-black rounded-lg overflow-hidden relative">
              <video
                ref={videoRef}
                className="w-full h-auto"
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                crossOrigin="anonymous"
                controls
              >
                <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
                
                {/* 原生字幕轨道 */}
                {!showCustomSubtitle && (
                  <>
                    <track
                      kind="subtitles"
                      src="/api/subtitles/test_simple.vtt"
                      srcLang="zh"
                      label="测试字幕"
                      default={selectedSubtitle === 'zh-ko'}
                    />
                    <track
                      kind="subtitles"
                      src="/api/subtitles/default_zh.vtt"
                      srcLang="zh"
                      label="中文字幕"
                      default={selectedSubtitle === 'zh'}
                    />
                  </>
                )}
                
                您的浏览器不支持视频播放。
              </video>

              {/* 自定义字幕覆盖层 */}
              {showCustomSubtitle && currentSubtitleText && selectedSubtitle !== 'none' && (
                <div className="absolute bottom-4 left-0 right-0 flex justify-center px-4">
                  <div className="bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg text-center max-w-4xl">
                    <div 
                      className="text-lg font-bold leading-relaxed whitespace-pre-line"
                      style={{
                        textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',
                        lineHeight: selectedSubtitle === 'zh-ko' ? '1.3' : '1.5'
                      }}
                    >
                      {currentSubtitleText}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 控制按钮 */}
            <div className="flex space-x-4">
              <button
                onClick={togglePlay}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                {isPlaying ? '暂停' : '播放'}
              </button>
            </div>
          </div>

          {/* 控制面板 */}
          <div className="space-y-6">
            {/* 字幕显示方式 */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3">字幕显示方式</h3>
              <div className="space-y-2">
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="subtitleMode"
                    checked={!showCustomSubtitle}
                    onChange={() => handleSubtitleModeChange(false)}
                    className="text-blue-600"
                  />
                  <span className="text-gray-300">原生HTML字幕</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="subtitleMode"
                    checked={showCustomSubtitle}
                    onChange={() => handleSubtitleModeChange(true)}
                    className="text-blue-600"
                  />
                  <span className="text-gray-300">自定义覆盖字幕</span>
                </label>
              </div>
            </div>

            {/* 字幕语言选择 */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3">字幕语言</h3>
              <div className="space-y-2">
                {subtitleTracks.map((track) => (
                  <button
                    key={track.id}
                    onClick={() => handleSubtitleChange(track.id)}
                    className={`w-full p-2 rounded text-left transition-colors ${
                      selectedSubtitle === track.id
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {track.label}
                  </button>
                ))}
              </div>
            </div>

            {/* 当前状态 */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3">当前状态</h3>
              <div className="text-gray-300 text-sm space-y-1">
                <p>显示方式: {showCustomSubtitle ? '自定义覆盖' : '原生HTML'}</p>
                <p>选中语言: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>
                <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>
                {showCustomSubtitle && currentSubtitleText && (
                  <div className="mt-2 p-2 bg-gray-700 rounded">
                    <p className="text-yellow-400 text-xs">当前字幕:</p>
                    <p className="text-white text-xs whitespace-pre-line">{currentSubtitleText}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-2">解决方案说明</h3>
          <div className="text-gray-300 text-sm space-y-2">
            <p><strong>原生HTML字幕</strong>: 使用浏览器内置的字幕功能，依赖浏览器渲染</p>
            <p><strong>自定义覆盖字幕</strong>: 使用JavaScript和CSS覆盖层显示字幕，完全可控</p>
            <p><strong>推荐</strong>: 如果原生字幕不显示，使用自定义覆盖字幕作为备选方案</p>
            <p><strong>优势</strong>: 自定义字幕可以确保在所有浏览器中正常显示，样式完全可控</p>
          </div>
        </div>
      </div>
    </div>
  )
}
