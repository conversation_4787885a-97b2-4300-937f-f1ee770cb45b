import { NextRequest, NextResponse } from 'next/server'
import { readFile } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    const filename = params.filename
    
    // 验证文件名格式
    if (!filename.endsWith('.vtt')) {
      return NextResponse.json(
        { error: '无效的字幕文件格式' },
        { status: 400 }
      )
    }

    // 构建文件路径
    const subtitlePath = join(process.cwd(), 'public', 'subtitles', filename)
    
    // 检查文件是否存在
    if (!existsSync(subtitlePath)) {
      return NextResponse.json(
        { error: '字幕文件不存在' },
        { status: 404 }
      )
    }

    // 读取文件内容
    const content = await readFile(subtitlePath, 'utf-8')

    // 返回字幕文件，设置正确的MIME类型和CORS头
    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': 'text/vtt; charset=utf-8',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Cache-Control': 'public, max-age=3600'
      }
    })

  } catch (error) {
    console.error('字幕文件读取失败:', error)
    return NextResponse.json(
      { error: '字幕文件读取失败' },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    }
  })
}
