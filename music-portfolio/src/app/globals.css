@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --background: #111827;
  --foreground: #f9fafb;
}

body {
  background: var(--background);
  color: var(--foreground);
}

/* 视频字幕样式优化 */
video::cue {
  background-color: rgba(0, 0, 0, 0.9) !important;
  color: white !important;
  font-size: 18px !important;
  line-height: 1.4 !important;
  text-align: center !important;
  padding: 6px 12px !important;
  border-radius: 4px !important;
  white-space: pre-line !important;
  font-family: Arial, sans-serif !important;
  font-weight: bold !important;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
}

/* 双语字幕样式 */
video::cue(.bilingual) {
  line-height: 1.3 !important;
  font-size: 16px !important;
}

/* 确保字幕在视频底部正确显示 */
video::-webkit-media-text-track-display {
  bottom: 30px !important;
  position: absolute !important;
  width: 100% !important;
  text-align: center !important;
  z-index: 1000 !important;
}

/* 强制显示字幕容器 */
video::-webkit-media-text-track-container {
  display: block !important;
  position: absolute !important;
  bottom: 0 !important;
  width: 100% !important;
  text-align: center !important;
  z-index: 1000 !important;
}

/* 字幕背景 */
video::-webkit-media-text-track-background {
  background-color: rgba(0, 0, 0, 0.8) !important;
  border-radius: 4px !important;
}
