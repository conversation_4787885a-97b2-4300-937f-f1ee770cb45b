@import "tailwindcss/base";
@import "tailwindcss/components";
@import "tailwindcss/utilities";

:root {
  --background: #111827;
  --foreground: #f9fafb;
}

body {
  background: var(--background);
  color: var(--foreground);
}

/* 视频字幕样式优化 */
video::cue {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 16px;
  line-height: 1.4;
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
  white-space: pre-line;
}

/* 双语字幕样式 */
video::cue(.bilingual) {
  line-height: 1.2;
  font-size: 14px;
}

/* 确保字幕在视频底部正确显示 */
video::-webkit-media-text-track-display {
  bottom: 20px;
  position: absolute;
  width: 100%;
  text-align: center;
}
