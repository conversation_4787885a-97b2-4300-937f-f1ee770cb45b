'use client'

import { useState, useRef, useEffect } from 'react'

export default function SubtitleDisplayTestPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [selectedSubtitle, setSelectedSubtitle] = useState('zh-ko')
  const [isPlaying, setIsPlaying] = useState(false)
  const [debugInfo, setDebugInfo] = useState<string[]>([])
  const [currentTime, setCurrentTime] = useState(0)

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`])
  }

  const subtitleTracks = [
    {
      id: 'none',
      label: '无字幕',
      src: ''
    },
    {
      id: 'zh-ko',
      label: '中韩字幕 (中文+韩文)',
      src: '/api/subtitles/default_zh-ko.vtt'
    }
  ]

  const handleSubtitleChange = async (subtitleId: string) => {
    const video = videoRef.current
    if (!video) return

    addDebugInfo(`切换字幕: ${subtitleId}`)

    // 禁用所有现有字幕轨道
    const tracks = video.textTracks
    for (let i = 0; i < tracks.length; i++) {
      tracks[i].mode = 'disabled'
      addDebugInfo(`禁用轨道 ${i}: ${tracks[i].label}`)
    }

    if (subtitleId === 'none') {
      setSelectedSubtitle('none')
      return
    }

    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)
    if (selectedTrack && selectedTrack.src) {
      setSelectedSubtitle(subtitleId)
      addDebugInfo(`选中轨道: ${selectedTrack.label}, 文件: ${selectedTrack.src}`)

      // 清除现有的字幕轨道元素
      const existingTracks = video.querySelectorAll('track')
      existingTracks.forEach((track, index) => {
        track.remove()
        addDebugInfo(`移除现有轨道 ${index}`)
      })

      // 创建新的字幕轨道元素
      const trackElement = document.createElement('track')
      trackElement.kind = 'subtitles'
      trackElement.src = selectedTrack.src
      trackElement.srcLang = 'zh'
      trackElement.label = selectedTrack.label
      trackElement.default = true

      addDebugInfo(`创建新轨道: ${trackElement.label}`)

      // 添加事件监听器
      trackElement.addEventListener('load', () => {
        addDebugInfo(`轨道加载成功: ${trackElement.label}`)
        addDebugInfo(`字幕条目数量: ${trackElement.track.cues?.length || 0}`)
        
        // 强制启用字幕
        trackElement.track.mode = 'disabled'
        setTimeout(() => {
          trackElement.track.mode = 'showing'
          addDebugInfo(`轨道模式设置为: ${trackElement.track.mode}`)
          
          // 检查当前时间的字幕
          if (trackElement.track.cues && trackElement.track.cues.length > 0) {
            const currentCue = Array.from(trackElement.track.cues).find(cue => 
              video.currentTime >= cue.startTime && video.currentTime <= cue.endTime
            )
            if (currentCue) {
              addDebugInfo(`当前时间字幕: ${currentCue.text}`)
            }
          }
        }, 100)
      })

      trackElement.addEventListener('error', (e) => {
        addDebugInfo(`轨道加载失败: ${e}`)
      })

      // 添加到video元素
      video.appendChild(trackElement)
      addDebugInfo(`轨道已添加到视频元素`)
    }
  }

  const handleTimeUpdate = () => {
    const video = videoRef.current
    if (video) {
      setCurrentTime(video.currentTime)
      
      // 检查当前活跃的字幕
      const tracks = video.textTracks
      for (let i = 0; i < tracks.length; i++) {
        const track = tracks[i]
        if (track.mode === 'showing' && track.activeCues && track.activeCues.length > 0) {
          const activeCue = track.activeCues[0]
          // 只在字幕变化时记录，避免过多日志
          if (activeCue && activeCue.text) {
            // 可以在这里添加当前字幕的调试信息
          }
        }
      }
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  useEffect(() => {
    // 初始化中韩字幕
    setTimeout(() => {
      handleSubtitleChange('zh-ko')
    }, 1000)
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">字幕显示测试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 视频播放器 */}
          <div className="space-y-4">
            <div className="bg-black rounded-lg overflow-hidden relative">
              <video
                ref={videoRef}
                className="w-full h-auto"
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onTimeUpdate={handleTimeUpdate}
                crossOrigin="anonymous"
                controls
              >
                <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
                {/* 直接在HTML中添加字幕轨道 */}
                <track
                  kind="subtitles"
                  src="/api/subtitles/default_zh-ko.vtt"
                  srcLang="zh"
                  label="中韩字幕"
                  default
                />
                您的浏览器不支持视频播放。
              </video>
            </div>

            {/* 控制按钮 */}
            <div className="flex space-x-4">
              <button
                onClick={togglePlay}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                {isPlaying ? '暂停' : '播放'}
              </button>
              
              <button
                onClick={() => handleSubtitleChange('zh-ko')}
                className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
              >
                重新加载字幕
              </button>
            </div>

            {/* 字幕选择 */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-2">字幕选择</h3>
              <div className="space-y-2">
                {subtitleTracks.map((track) => (
                  <button
                    key={track.id}
                    onClick={() => handleSubtitleChange(track.id)}
                    className={`w-full p-2 rounded text-left transition-colors ${
                      selectedSubtitle === track.id
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {track.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* 调试信息 */}
          <div className="space-y-4">
            {/* 状态信息 */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-2">当前状态</h3>
              <div className="text-gray-300 space-y-1 text-sm">
                <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>
                <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>
                <p>当前时间: {currentTime.toFixed(2)}s</p>
                <p>视频文本轨道数量: {videoRef.current?.textTracks.length || 0}</p>
                
                {videoRef.current && Array.from(videoRef.current.textTracks).map((track, index) => (
                  <div key={index} className="ml-2 text-xs">
                    <p>轨道 {index}: {track.label} (模式: {track.mode})</p>
                    <p>字幕条目: {track.cues?.length || 0}</p>
                    <p>活跃字幕: {track.activeCues?.length || 0}</p>
                    {track.activeCues && track.activeCues.length > 0 && (
                      <p className="text-yellow-400">当前字幕: {track.activeCues[0].text}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* 调试日志 */}
            <div className="bg-gray-800 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-2">调试日志</h3>
              <div className="bg-gray-900 rounded p-3 max-h-60 overflow-y-auto">
                {debugInfo.map((info, index) => (
                  <div key={index} className="text-xs text-gray-300 mb-1">
                    {info}
                  </div>
                ))}
              </div>
              <button
                onClick={() => setDebugInfo([])}
                className="mt-2 px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600"
              >
                清除日志
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-2">测试说明</h3>
          <div className="text-gray-300 text-sm space-y-2">
            <p>1. 点击播放按钮开始播放视频</p>
            <p>2. 观察视频底部是否显示中韩双语字幕</p>
            <p>3. 查看右侧的调试信息，确认字幕轨道状态</p>
            <p>4. 如果字幕不显示，点击"重新加载字幕"按钮</p>
            <p>5. 检查"活跃字幕"是否有内容显示</p>
          </div>
        </div>
      </div>
    </div>
  )
}
