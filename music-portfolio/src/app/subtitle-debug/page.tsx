'use client'

import { useState, useRef, useEffect } from 'react'

export default function SubtitleDebugPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [selectedSubtitle, setSelectedSubtitle] = useState('zh')
  const [isPlaying, setIsPlaying] = useState(false)
  const [debugInfo, setDebugInfo] = useState<string[]>([])
  const [textTracks, setTextTracks] = useState<any[]>([])

  const addDebugInfo = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setDebugInfo(prev => [...prev, `[${timestamp}] ${message}`])
  }

  const subtitleTracks = [
    {
      id: 'none',
      label: '无字幕',
      language: 'none',
      src: ''
    },
    {
      id: 'zh',
      label: '中文字幕',
      language: 'zh',
      src: '/subtitles/default_zh.vtt'
    },
    {
      id: 'zh-ko',
      label: '中韩字幕 (中文+韩文)',
      language: 'zh-ko',
      src: '/subtitles/default_zh-ko.vtt'
    },
    {
      id: 'zh-en',
      label: '中英字幕 (中文+英文)',
      language: 'zh-en',
      src: '/subtitles/default_zh-en.vtt'
    }
  ]

  const updateTextTracksInfo = () => {
    const video = videoRef.current
    if (!video) return

    const tracks = Array.from(video.textTracks).map((track, index) => ({
      index,
      kind: track.kind,
      label: track.label,
      language: track.language,
      mode: track.mode,
      cues: track.cues ? track.cues.length : 0
    }))
    setTextTracks(tracks)
  }

  const handleSubtitleChange = async (subtitleId: string) => {
    const video = videoRef.current
    if (!video) return

    addDebugInfo(`开始切换字幕: ${subtitleId}`)

    // 禁用所有现有字幕轨道
    const tracks = video.textTracks
    for (let i = 0; i < tracks.length; i++) {
      tracks[i].mode = 'disabled'
      addDebugInfo(`禁用轨道 ${i}: ${tracks[i].label}`)
    }

    if (subtitleId === 'none') {
      setSelectedSubtitle('none')
      addDebugInfo('选择无字幕')
      updateTextTracksInfo()
      return
    }

    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)
    if (selectedTrack && selectedTrack.src) {
      setSelectedSubtitle(subtitleId)
      addDebugInfo(`选中字幕轨道: ${selectedTrack.label}`)
      addDebugInfo(`字幕文件路径: ${selectedTrack.src}`)

      // 清除现有的字幕轨道元素
      const existingTracks = video.querySelectorAll('track')
      addDebugInfo(`清除现有轨道元素: ${existingTracks.length} 个`)
      existingTracks.forEach((track, index) => {
        addDebugInfo(`移除轨道元素 ${index}: ${track.label}`)
        track.remove()
      })

      // 创建新的字幕轨道元素
      const trackElement = document.createElement('track')
      trackElement.kind = 'subtitles'
      trackElement.src = selectedTrack.src
      trackElement.srcLang = selectedTrack.language.split('-')[0]
      trackElement.label = selectedTrack.label
      trackElement.default = true

      addDebugInfo(`创建新轨道元素: ${trackElement.label}`)
      addDebugInfo(`轨道语言: ${trackElement.srcLang}`)

      // 添加事件监听器
      trackElement.addEventListener('load', () => {
        addDebugInfo(`字幕文件加载成功: ${selectedTrack.src}`)
        trackElement.track.mode = 'showing'
        addDebugInfo(`设置轨道模式为 showing`)
        updateTextTracksInfo()
      })

      trackElement.addEventListener('error', (e) => {
        addDebugInfo(`字幕文件加载失败: ${e}`)
      })

      // 添加到video元素
      video.appendChild(trackElement)
      addDebugInfo(`轨道元素已添加到视频`)

      // 延迟启用字幕显示
      setTimeout(() => {
        const textTracks = video.textTracks
        addDebugInfo(`当前文本轨道数量: ${textTracks.length}`)
        
        for (let i = 0; i < textTracks.length; i++) {
          const track = textTracks[i]
          addDebugInfo(`轨道 ${i}: ${track.label}, 模式: ${track.mode}, 提示数: ${track.cues ? track.cues.length : 0}`)
          
          if (track.label === selectedTrack.label) {
            track.mode = 'showing'
            addDebugInfo(`成功启用字幕轨道: ${track.label}`)
            
            // 检查字幕内容
            if (track.cues && track.cues.length > 0) {
              addDebugInfo(`字幕提示数量: ${track.cues.length}`)
              const firstCue = track.cues[0]
              addDebugInfo(`第一个字幕: "${firstCue.text}"`)
            }
            break
          }
        }
        updateTextTracksInfo()
      }, 500)
    }
  }

  const clearDebugInfo = () => {
    setDebugInfo([])
  }

  const testSubtitleFile = async (src: string) => {
    try {
      const response = await fetch(src)
      if (response.ok) {
        const content = await response.text()
        addDebugInfo(`字幕文件 ${src} 可访问，内容长度: ${content.length}`)
        addDebugInfo(`文件内容预览: ${content.substring(0, 100)}...`)
      } else {
        addDebugInfo(`字幕文件 ${src} 访问失败: ${response.status}`)
      }
    } catch (error) {
      addDebugInfo(`字幕文件 ${src} 测试失败: ${error}`)
    }
  }

  useEffect(() => {
    // 初始化
    addDebugInfo('页面初始化')
    updateTextTracksInfo()
    
    // 测试所有字幕文件
    subtitleTracks.forEach(track => {
      if (track.src) {
        testSubtitleFile(track.src)
      }
    })
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 p-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-6">字幕调试工具</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 视频播放器 */}
          <div className="bg-gray-800 rounded-lg p-4">
            <h2 className="text-xl font-semibold text-white mb-4">视频播放器</h2>
            <div className="bg-black rounded-lg overflow-hidden mb-4">
              <video
                ref={videoRef}
                className="w-full h-auto"
                onPlay={() => {
                  setIsPlaying(true)
                  addDebugInfo('视频开始播放')
                }}
                onPause={() => {
                  setIsPlaying(false)
                  addDebugInfo('视频暂停')
                }}
                onLoadedMetadata={() => {
                  addDebugInfo('视频元数据加载完成')
                  updateTextTracksInfo()
                }}
                crossOrigin="anonymous"
                controls
              >
                <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
                您的浏览器不支持视频播放。
              </video>
            </div>

            {/* 字幕选择 */}
            <div className="grid grid-cols-2 gap-2 mb-4">
              {subtitleTracks.map((track) => (
                <button
                  key={track.id}
                  onClick={() => handleSubtitleChange(track.id)}
                  className={`p-2 rounded text-sm font-medium transition-colors ${
                    selectedSubtitle === track.id
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {track.label}
                </button>
              ))}
            </div>

            {/* 当前状态 */}
            <div className="text-gray-300 text-sm">
              <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>
              <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>
            </div>
          </div>

          {/* 调试信息 */}
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-white">调试信息</h2>
              <button
                onClick={clearDebugInfo}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                清除
              </button>
            </div>
            <div className="bg-gray-900 rounded p-3 h-64 overflow-y-auto">
              {debugInfo.map((info, index) => (
                <div key={index} className="text-green-400 text-xs mb-1 font-mono">
                  {info}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 文本轨道信息 */}
        <div className="bg-gray-800 rounded-lg p-4 mt-6">
          <h2 className="text-xl font-semibold text-white mb-4">当前文本轨道</h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm text-gray-300">
              <thead>
                <tr className="border-b border-gray-600">
                  <th className="text-left p-2">索引</th>
                  <th className="text-left p-2">类型</th>
                  <th className="text-left p-2">标签</th>
                  <th className="text-left p-2">语言</th>
                  <th className="text-left p-2">模式</th>
                  <th className="text-left p-2">提示数</th>
                </tr>
              </thead>
              <tbody>
                {textTracks.map((track) => (
                  <tr key={track.index} className="border-b border-gray-700">
                    <td className="p-2">{track.index}</td>
                    <td className="p-2">{track.kind}</td>
                    <td className="p-2">{track.label}</td>
                    <td className="p-2">{track.language}</td>
                    <td className="p-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        track.mode === 'showing' ? 'bg-green-600' : 
                        track.mode === 'disabled' ? 'bg-red-600' : 'bg-gray-600'
                      }`}>
                        {track.mode}
                      </span>
                    </td>
                    <td className="p-2">{track.cues}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
