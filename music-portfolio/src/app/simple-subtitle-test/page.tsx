'use client'

import { useRef, useEffect, useState } from 'react'

export default function SimpleSubtitleTestPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [trackInfo, setTrackInfo] = useState<any[]>([])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const updateTrackInfo = () => {
      const tracks = Array.from(video.textTracks)
      const info = tracks.map((track, index) => ({
        index,
        label: track.label,
        language: track.language,
        mode: track.mode,
        cues: track.cues?.length || 0,
        activeCues: track.activeCues?.length || 0,
        currentCue: track.activeCues && track.activeCues.length > 0 ? track.activeCues[0].text : null
      }))
      setTrackInfo(info)
    }

    // 监听字幕轨道变化
    video.addEventListener('loadedmetadata', updateTrackInfo)
    video.addEventListener('timeupdate', updateTrackInfo)

    // 手动启用字幕
    const enableSubtitles = () => {
      const tracks = video.textTracks
      for (let i = 0; i < tracks.length; i++) {
        tracks[i].mode = 'showing'
        console.log(`启用轨道 ${i}:`, tracks[i].label)
      }
      updateTrackInfo()
    }

    // 延迟启用字幕
    setTimeout(enableSubtitles, 1000)

    return () => {
      video.removeEventListener('loadedmetadata', updateTrackInfo)
      video.removeEventListener('timeupdate', updateTrackInfo)
    }
  }, [])

  const forceEnableSubtitles = () => {
    const video = videoRef.current
    if (!video) return

    const tracks = video.textTracks
    for (let i = 0; i < tracks.length; i++) {
      tracks[i].mode = 'showing'
      console.log(`强制启用轨道 ${i}:`, tracks[i].label, tracks[i].mode)
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">简单字幕测试</h1>
        
        <div className="space-y-6">
          {/* 视频播放器 */}
          <div className="bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-auto"
              controls
              crossOrigin="anonymous"
              style={{
                // 确保字幕样式
                '--webkit-media-text-track-display': 'block'
              } as React.CSSProperties}
            >
              <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
              
              {/* 测试字幕轨道 */}
              <track
                kind="subtitles"
                src="/api/subtitles/test_simple.vtt"
                srcLang="zh"
                label="简单测试字幕"
                default
              />
              <track
                kind="subtitles"
                src="/api/subtitles/default_zh-ko.vtt"
                srcLang="zh"
                label="中韩字幕"
              />
              
              您的浏览器不支持视频播放。
            </video>
          </div>

          {/* 控制按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={forceEnableSubtitles}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              强制启用字幕
            </button>
          </div>

          {/* 字幕轨道信息 */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">字幕轨道信息</h2>
            {trackInfo.length === 0 ? (
              <p className="text-gray-400">没有检测到字幕轨道</p>
            ) : (
              <div className="space-y-4">
                {trackInfo.map((track) => (
                  <div key={track.index} className="bg-gray-700 rounded p-4">
                    <h3 className="text-lg font-medium text-white mb-2">
                      轨道 {track.index}: {track.label}
                    </h3>
                    <div className="text-sm text-gray-300 space-y-1">
                      <p>语言: {track.language}</p>
                      <p>模式: <span className={track.mode === 'showing' ? 'text-green-400' : 'text-red-400'}>{track.mode}</span></p>
                      <p>字幕条目数: {track.cues}</p>
                      <p>活跃字幕数: {track.activeCues}</p>
                      {track.currentCue && (
                        <div className="mt-2 p-2 bg-gray-600 rounded">
                          <p className="text-yellow-400 font-medium">当前字幕:</p>
                          <p className="text-white whitespace-pre-line">{track.currentCue}</p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 说明 */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-white mb-4">测试说明</h2>
            <div className="text-gray-300 text-sm space-y-2">
              <p>1. 这个页面使用HTML原生的 &lt;track&gt; 元素来加载字幕</p>
              <p>2. 视频加载后会自动尝试启用字幕</p>
              <p>3. 如果字幕没有显示，点击"强制启用字幕"按钮</p>
              <p>4. 观察下方的字幕轨道信息，确认字幕是否正确加载</p>
              <p>5. 如果"当前字幕"有内容但视频上没有显示，可能是浏览器渲染问题</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
