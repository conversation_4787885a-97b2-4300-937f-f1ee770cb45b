'use client'

import { useState, useRef } from 'react'

export default function SubtitleSimplePage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [selectedSubtitle, setSelectedSubtitle] = useState('zh')

  const subtitleTracks = [
    {
      id: 'none',
      label: '无字幕',
      src: ''
    },
    {
      id: 'zh',
      label: '中文字幕',
      src: '/subtitles/default_zh.vtt'
    },
    {
      id: 'zh-ko',
      label: '中韩字幕',
      src: '/subtitles/default_zh-ko.vtt'
    },
    {
      id: 'zh-en',
      label: '中英字幕',
      src: '/subtitles/default_zh-en.vtt'
    }
  ]

  const handleSubtitleChange = (subtitleId: string) => {
    const video = videoRef.current
    if (!video) return

    console.log('切换字幕:', subtitleId)

    // 禁用所有现有字幕轨道
    const tracks = video.textTracks
    for (let i = 0; i < tracks.length; i++) {
      tracks[i].mode = 'disabled'
    }

    if (subtitleId === 'none') {
      setSelectedSubtitle('none')
      return
    }

    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)
    if (selectedTrack && selectedTrack.src) {
      setSelectedSubtitle(subtitleId)

      // 清除现有的字幕轨道元素
      const existingTracks = video.querySelectorAll('track')
      existingTracks.forEach(track => track.remove())

      // 创建新的字幕轨道元素
      const trackElement = document.createElement('track')
      trackElement.kind = 'subtitles'
      trackElement.src = selectedTrack.src
      trackElement.srcLang = 'zh'
      trackElement.label = selectedTrack.label
      trackElement.default = true

      // 添加到video元素
      video.appendChild(trackElement)

      // 启用字幕显示
      trackElement.addEventListener('load', () => {
        console.log('字幕文件加载完成:', selectedTrack.src)
        trackElement.track.mode = 'showing'
        console.log('字幕轨道已启用:', trackElement.track.label)
        
        // 检查字幕内容
        if (trackElement.track.cues && trackElement.track.cues.length > 0) {
          console.log('字幕提示数量:', trackElement.track.cues.length)
          console.log('第一个字幕内容:', trackElement.track.cues[0].text)
        }
      })

      trackElement.addEventListener('error', (e) => {
        console.error('字幕文件加载失败:', e)
      })
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">简化字幕测试</h1>
        
        <div className="bg-black rounded-lg overflow-hidden mb-6">
          <video
            ref={videoRef}
            className="w-full h-auto"
            crossOrigin="anonymous"
            controls
            onLoadedMetadata={() => {
              console.log('视频元数据加载完成')
              // 自动加载默认字幕
              setTimeout(() => {
                handleSubtitleChange('zh')
              }, 1000)
            }}
          >
            <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
            您的浏览器不支持视频播放。
          </video>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">字幕选择</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {subtitleTracks.map((track) => (
              <button
                key={track.id}
                onClick={() => handleSubtitleChange(track.id)}
                className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                  selectedSubtitle === track.id
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {track.label}
              </button>
            ))}
          </div>

          <div className="text-gray-300">
            <h3 className="text-lg font-semibold mb-2">测试说明</h3>
            <ul className="text-sm space-y-1">
              <li>• 选择"中韩字幕"应该看到中文和韩文两行</li>
              <li>• 选择"中英字幕"应该看到中文和英文两行</li>
              <li>• 打开浏览器开发者工具查看控制台日志</li>
              <li>• 如果只看到中文，说明双语字幕显示有问题</li>
            </ul>
          </div>

          <div className="mt-4 p-4 bg-gray-700 rounded">
            <h4 className="text-white font-semibold mb-2">当前状态</h4>
            <p className="text-gray-300">选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>
          </div>
        </div>
      </div>
    </div>
  )
}
