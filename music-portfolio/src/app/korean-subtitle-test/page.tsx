'use client'

import { useState, useRef, useEffect } from 'react'

export default function KoreanSubtitleTestPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [selectedSubtitle, setSelectedSubtitle] = useState('korean')
  const [isPlaying, setIsPlaying] = useState(false)
  const [debugInfo, setDebugInfo] = useState<string[]>([])

  const addDebugInfo = (info: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`])
  }

  const subtitleTracks = [
    {
      id: 'none',
      label: '无字幕',
      language: 'none',
      src: ''
    },
    {
      id: 'korean',
      label: '한국어 (韩文)',
      language: 'ko',
      src: '/api/subtitles/movie_1755665391178_ko.vtt'
    },
    {
      id: 'zh-ko',
      label: '中韩字幕 (中文+韩文)',
      language: 'zh-ko',
      src: '/api/subtitles/default_zh-ko.vtt'
    }
  ]

  const handleSubtitleChange = async (subtitleId: string) => {
    const video = videoRef.current
    if (!video) return

    addDebugInfo(`切换字幕: ${subtitleId}`)

    // 禁用所有现有字幕轨道
    const tracks = video.textTracks
    for (let i = 0; i < tracks.length; i++) {
      tracks[i].mode = 'disabled'
      addDebugInfo(`禁用轨道 ${i}: ${tracks[i].label}`)
    }

    if (subtitleId === 'none') {
      setSelectedSubtitle('none')
      return
    }

    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)
    if (selectedTrack && selectedTrack.src) {
      setSelectedSubtitle(subtitleId)
      addDebugInfo(`选中轨道: ${selectedTrack.label}, 文件: ${selectedTrack.src}`)

      // 检查字幕文件是否存在
      try {
        const response = await fetch(selectedTrack.src, { method: 'HEAD' })
        addDebugInfo(`字幕文件检查: ${response.ok ? '存在' : '不存在'} (${response.status})`)
      } catch (error) {
        addDebugInfo(`字幕文件检查失败: ${error}`)
      }

      // 清除现有的字幕轨道元素
      const existingTracks = video.querySelectorAll('track')
      existingTracks.forEach((track, index) => {
        track.remove()
        addDebugInfo(`移除现有轨道 ${index}`)
      })

      // 创建新的字幕轨道元素
      const trackElement = document.createElement('track')
      trackElement.kind = 'subtitles'
      trackElement.src = selectedTrack.src
      trackElement.srcLang = selectedTrack.language.split('-')[0]
      trackElement.label = selectedTrack.label
      trackElement.default = true

      addDebugInfo(`创建新轨道: ${trackElement.label}, 语言: ${trackElement.srcLang}`)

      // 添加事件监听器
      trackElement.addEventListener('load', () => {
        addDebugInfo(`轨道加载成功: ${trackElement.label}`)
        trackElement.track.mode = 'showing'
        addDebugInfo(`轨道模式设置为: showing`)
      })

      trackElement.addEventListener('error', (e) => {
        addDebugInfo(`轨道加载失败: ${e}`)
      })

      // 添加到video元素
      video.appendChild(trackElement)
      addDebugInfo(`轨道已添加到视频元素`)

      // 延迟启用字幕显示
      setTimeout(() => {
        const textTracks = video.textTracks
        addDebugInfo(`当前文本轨道数量: ${textTracks.length}`)
        
        for (let i = 0; i < textTracks.length; i++) {
          const track = textTracks[i]
          addDebugInfo(`轨道 ${i}: ${track.label}, 模式: ${track.mode}, 语言: ${track.language}`)
          
          if (track.label === selectedTrack.label) {
            track.mode = 'showing'
            addDebugInfo(`成功启用轨道: ${track.label}`)
            break
          }
        }
      }, 200)
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  useEffect(() => {
    // 初始化韩语字幕
    setTimeout(() => {
      handleSubtitleChange('korean')
    }, 1000)
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">韩语字幕测试页面</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 视频播放器 */}
          <div className="bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-auto"
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              crossOrigin="anonymous"
              controls
              style={{
                '--subtitle-font-size': '18px',
                '--subtitle-line-height': '1.4',
                '--subtitle-background': 'rgba(0, 0, 0, 0.8)',
                '--subtitle-color': 'white'
              } as React.CSSProperties}
            >
              <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
              您的浏览器不支持视频播放。
            </video>
          </div>

          {/* 控制面板 */}
          <div className="space-y-6">
            {/* 字幕选择 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-white mb-4">字幕选择</h2>
              <div className="space-y-2">
                {subtitleTracks.map((track) => (
                  <button
                    key={track.id}
                    onClick={() => handleSubtitleChange(track.id)}
                    className={`w-full p-3 rounded-lg text-left font-medium transition-colors ${
                      selectedSubtitle === track.id
                        ? 'bg-red-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    }`}
                  >
                    {track.label}
                    {track.src && (
                      <div className="text-xs text-gray-400 mt-1">
                        文件: {track.src}
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* 状态信息 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-2">当前状态</h3>
              <div className="text-gray-300 space-y-1 text-sm">
                <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>
                <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>
                <p>视频文本轨道数量: {videoRef.current?.textTracks.length || 0}</p>
              </div>
            </div>

            {/* 调试信息 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-2">调试信息</h3>
              <div className="bg-gray-900 rounded p-3 max-h-60 overflow-y-auto">
                {debugInfo.map((info, index) => (
                  <div key={index} className="text-xs text-gray-300 mb-1">
                    {info}
                  </div>
                ))}
              </div>
              <button
                onClick={() => setDebugInfo([])}
                className="mt-2 px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600"
              >
                清除日志
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-white mb-2">测试说明</h3>
          <div className="text-gray-300 text-sm space-y-2">
            <p>1. 点击"한국어 (韩文)"按钮测试韩语字幕</p>
            <p>2. 点击"中韩字幕"按钮测试双语字幕</p>
            <p>3. 观察调试信息中的字幕加载过程</p>
            <p>4. 检查视频是否显示对应的字幕</p>
            <p>5. 如果字幕不显示，查看浏览器控制台的错误信息</p>
          </div>
        </div>
      </div>
    </div>
  )
}
