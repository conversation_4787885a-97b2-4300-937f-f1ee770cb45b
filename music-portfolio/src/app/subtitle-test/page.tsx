'use client'

import { useState, useRef, useEffect } from 'react'

export default function SubtitleTestPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [selectedSubtitle, setSelectedSubtitle] = useState('zh')
  const [isPlaying, setIsPlaying] = useState(false)

  const subtitleTracks = [
    {
      id: 'none',
      label: '无字幕',
      language: 'none',
      src: ''
    },
    {
      id: 'zh',
      label: '中文字幕',
      language: 'zh',
      src: '/subtitles/default_zh.vtt'
    },
    {
      id: 'zh-ko',
      label: '中韩字幕 (中文+韩文)',
      language: 'zh-ko',
      src: '/subtitles/default_zh-ko.vtt'
    },
    {
      id: 'zh-en',
      label: '中英字幕 (中文+英文)',
      language: 'zh-en',
      src: '/subtitles/default_zh-en.vtt'
    }
  ]

  const handleSubtitleChange = (subtitleId: string) => {
    const video = videoRef.current
    if (!video) return

    console.log('切换字幕:', subtitleId)

    // 禁用所有现有字幕轨道
    const tracks = video.textTracks
    for (let i = 0; i < tracks.length; i++) {
      tracks[i].mode = 'disabled'
    }

    if (subtitleId === 'none') {
      setSelectedSubtitle('none')
      return
    }

    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)
    if (selectedTrack && selectedTrack.src) {
      setSelectedSubtitle(subtitleId)

      // 清除现有的字幕轨道元素
      const existingTracks = video.querySelectorAll('track')
      existingTracks.forEach(track => track.remove())

      // 创建新的字幕轨道元素
      const trackElement = document.createElement('track')
      trackElement.kind = 'subtitles'
      trackElement.src = selectedTrack.src
      trackElement.srcLang = selectedTrack.language.split('-')[0]
      trackElement.label = selectedTrack.label
      trackElement.default = true

      // 添加到video元素
      video.appendChild(trackElement)

      // 启用字幕显示
      setTimeout(() => {
        const textTracks = video.textTracks
        for (let i = 0; i < textTracks.length; i++) {
          const track = textTracks[i]
          if (track.label === selectedTrack.label) {
            track.mode = 'showing'
            console.log('成功启用字幕轨道:', track.label, '文件路径:', selectedTrack.src)
            break
          }
        }
      }, 200)
    }
  }

  const togglePlay = () => {
    const video = videoRef.current
    if (!video) return

    if (isPlaying) {
      video.pause()
    } else {
      video.play()
    }
  }

  useEffect(() => {
    // 初始化默认字幕
    setTimeout(() => {
      handleSubtitleChange('zh')
    }, 1000)
  }, [])

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">字幕功能测试页面</h1>
        
        <div className="bg-black rounded-lg overflow-hidden mb-6">
          <video
            ref={videoRef}
            className="w-full h-auto"
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            crossOrigin="anonymous"
            controls
            style={{
              '--subtitle-font-size': '18px',
              '--subtitle-line-height': '1.4',
              '--subtitle-background': 'rgba(0, 0, 0, 0.8)',
              '--subtitle-color': 'white'
            } as React.CSSProperties}
          >
            <source src="/uploads/movies/movie_1755665391178.mp4" type="video/mp4" />
            您的浏览器不支持视频播放。
          </video>
        </div>

        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">字幕选择</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {subtitleTracks.map((track) => (
              <button
                key={track.id}
                onClick={() => handleSubtitleChange(track.id)}
                className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                  selectedSubtitle === track.id
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {track.label}
              </button>
            ))}
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold text-white mb-2">当前状态</h3>
            <div className="text-gray-300 space-y-1">
              <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>
              <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>
            </div>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold text-white mb-2">测试说明</h3>
            <div className="text-gray-300 text-sm space-y-2">
              <p>1. 点击不同的字幕选项来测试字幕切换功能</p>
              <p>2. 中韩字幕应该显示中文和韩文两行</p>
              <p>3. 中英字幕应该显示中文和英文两行</p>
              <p>4. 检查浏览器控制台的日志信息</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
