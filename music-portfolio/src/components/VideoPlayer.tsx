'use client'

import { useState, useRef, useEffect } from 'react'
import { Play, Pause, Volume2, VolumeX, Maximize, X, Subtitles, Settings } from 'lucide-react'
import VideoDebugInfo from './VideoDebugInfo'

interface VideoPlayerProps {
  videoPath: string
  title: string
  onClose: () => void
}

interface SubtitleTrack {
  id: string
  label: string
  language: string
  src: string
}

export default function VideoPlayer({ videoPath, title, onClose }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showSubtitleMenu, setShowSubtitleMenu] = useState(false)
  const [selectedSubtitle, setSelectedSubtitle] = useState<string>('off')
  const [subtitleTracks, setSubtitleTracks] = useState<SubtitleTrack[]>([])
  const [isGeneratingSubtitles, setIsGeneratingSubtitles] = useState(false)
  const [showDebugInfo, setShowDebugInfo] = useState(false)

  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    // 只初始化字幕轨道，不自动加载视频
    initializeSubtitleTracks()

    const video = videoRef.current
    if (!video) return

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
      setIsLoading(false)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handleError = () => {
      setError('视频加载失败，请检查文件是否存在')
      setIsLoading(false)
    }

    const handleLoadStart = () => {
      setIsLoading(true)
      setError(null)
    }

    // 监听全屏状态变化
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    // 监听键盘事件
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && document.fullscreenElement) {
        document.exitFullscreen()
      } else if (e.key === ' ') {
        e.preventDefault()
        togglePlay()
      }
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('error', handleError)
    video.addEventListener('loadstart', handleLoadStart)
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('keydown', handleKeyDown)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('error', handleError)
      video.removeEventListener('loadstart', handleLoadStart)
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [])

  const togglePlay = async () => {
    const video = videoRef.current
    if (!video) return

    try {
      if (isPlaying) {
        video.pause()
        setIsPlaying(false)
      } else {
        // 如果视频还没有源，设置源并加载
        if (!video.src || video.readyState === 0) {
          setIsLoading(true)
          video.src = getAuthenticatedVideoUrl()
          video.load()

          // 等待视频可以播放
          await new Promise((resolve, reject) => {
            const onCanPlay = () => {
              video.removeEventListener('canplay', onCanPlay)
              video.removeEventListener('error', onError)
              resolve(void 0)
            }
            const onError = () => {
              video.removeEventListener('canplay', onCanPlay)
              video.removeEventListener('error', onError)
              reject(new Error('视频加载失败'))
            }
            video.addEventListener('canplay', onCanPlay)
            video.addEventListener('error', onError)

            // 5秒超时
            setTimeout(() => {
              video.removeEventListener('canplay', onCanPlay)
              video.removeEventListener('error', onError)
              reject(new Error('视频加载超时'))
            }, 5000)
          })
        }

        await video.play()
        setIsPlaying(true)
        setIsLoading(false)
      }
    } catch (error) {
      console.error('播放错误:', error)
      setError('视频播放失败: ' + (error as Error).message)
      setIsLoading(false)
      setIsPlaying(false)
    }
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current
    if (!video) return

    const newTime = parseFloat(e.target.value)
    video.currentTime = newTime
    setCurrentTime(newTime)
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current
    if (!video) return

    const newVolume = parseFloat(e.target.value)
    video.volume = newVolume
    setVolume(newVolume)
    setIsMuted(newVolume === 0)
  }

  const toggleMute = () => {
    const video = videoRef.current
    if (!video) return

    if (isMuted) {
      video.volume = volume
      setIsMuted(false)
    } else {
      video.volume = 0
      setIsMuted(true)
    }
  }

  const toggleFullscreen = async () => {
    const videoContainer = videoRef.current?.parentElement
    if (!videoContainer) return

    try {
      if (!document.fullscreenElement) {
        // 进入全屏
        await videoContainer.requestFullscreen()
        setIsFullscreen(true)
      } else {
        // 退出全屏
        await document.exitFullscreen()
        setIsFullscreen(false)
      }
    } catch (error) {
      console.error('全屏切换失败:', error)
    }
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // 初始化字幕轨道
  const initializeSubtitleTracks = () => {
    // 从视频路径提取文件名（不含扩展名）
    const videoFileName = videoPath.split('/').pop()?.replace(/\.[^/.]+$/, '') || 'default'

    const tracks: SubtitleTrack[] = [
      {
        id: 'none',
        label: '无字幕',
        language: 'none',
        src: ''
      },
      {
        id: 'zh',
        label: '中文字幕',
        language: 'zh',
        src: '/api/subtitles/default_zh.vtt'
      },
      {
        id: 'zh-ko',
        label: '中韩字幕 (中文+韩文)',
        language: 'zh-ko',
        src: '/api/subtitles/default_zh-ko.vtt'
      },
      {
        id: 'zh-en',
        label: '中英字幕 (中文+英文)',
        language: 'zh-en',
        src: '/api/subtitles/default_zh-en.vtt'
      },
      {
        id: 'korean',
        label: '한국어 (韩文)',
        language: 'ko',
        src: `/api/subtitles/${videoFileName}_ko.vtt` // 使用实际的文件名
      },
      {
        id: 'english',
        label: 'English (英文)',
        language: 'en',
        src: `/api/subtitles/${videoFileName}_en.vtt` // 使用实际的文件名
      }
    ]

    console.log('初始化字幕轨道:', tracks)
    setSubtitleTracks(tracks)
    // 默认选择中文字幕
    setSelectedSubtitle('zh')
  }

  // 生成字幕
  const generateSubtitles = async (language: string) => {
    setIsGeneratingSubtitles(true)
    try {
      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)

      const response = await fetch('/api/subtitles/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`
        },
        body: JSON.stringify({
          videoPath,
          language
        })
      })

      const result = await response.json()

      if (result.success) {
        // 更新字幕轨道
        setSubtitleTracks(prev =>
          prev.map(track =>
            track.language === language
              ? { ...track, src: result.subtitlePath }
              : track
          )
        )

        // 根据语言类型自动选择对应的字幕ID
        let subtitleId = 'english' // 默认
        switch (language) {
          case 'zh':
            subtitleId = 'zh'
            break
          case 'ko':
            subtitleId = 'korean'
            break
          case 'en':
            subtitleId = 'english'
            break
          case 'zh-ko':
            subtitleId = 'zh-ko'
            break
          case 'zh-en':
            subtitleId = 'zh-en'
            break
        }

        setSelectedSubtitle(subtitleId)

        // 动态添加字幕轨道到视频元素
        setTimeout(() => {
          const video = videoRef.current
          if (video) {
            // 清除现有的字幕轨道元素
            const existingTracks = video.querySelectorAll('track')
            existingTracks.forEach(track => track.remove())

            // 创建新的track元素
            const trackElement = document.createElement('track')
            trackElement.kind = 'subtitles'
            trackElement.src = result.subtitlePath
            trackElement.srcLang = language.split('-')[0] // 取主要语言代码
            trackElement.label = subtitleTracks.find(t => t.language === language)?.label || language
            trackElement.default = true

            console.log('生成字幕后添加轨道:', {
              src: result.subtitlePath,
              srcLang: language.split('-')[0],
              label: trackElement.label
            })

            // 添加到video元素
            video.appendChild(trackElement)

            // 启用字幕显示
            trackElement.addEventListener('load', () => {
              console.log('字幕轨道加载完成:', trackElement.label)
              // 先禁用再启用，确保刷新
              trackElement.track.mode = 'disabled'
              setTimeout(() => {
                trackElement.track.mode = 'showing'
                console.log('字幕轨道启用:', {
                  label: trackElement.label,
                  mode: trackElement.track.mode,
                  cues: trackElement.track.cues?.length || 0
                })
              }, 50)
            })

            // 添加错误处理
            trackElement.addEventListener('error', (e) => {
              console.error('字幕轨道加载失败:', e, trackElement.src)
            })
          }
        }, 100)

      } else {
        throw new Error(result.error || '字幕生成失败')
      }

    } catch (error) {
      console.error('字幕生成失败:', error)
      alert('字幕生成失败，请稍后重试')
    } finally {
      setIsGeneratingSubtitles(false)
    }
  }

  // 切换字幕
  const handleSubtitleChange = async (subtitleId: string, event?: React.MouseEvent) => {
    // 阻止事件冒泡，防止触发播放/暂停
    if (event) {
      event.preventDefault()
      event.stopPropagation()
    }

    const video = videoRef.current
    if (!video) return

    console.log('切换字幕:', subtitleId)

    // 禁用所有现有字幕轨道
    const tracks = video.textTracks
    for (let i = 0; i < tracks.length; i++) {
      tracks[i].mode = 'disabled'
    }

    if (subtitleId === 'off' || subtitleId === 'none') {
      setSelectedSubtitle('off')
      setShowSubtitleMenu(false)
      return
    }

    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)
    if (selectedTrack) {
      setSelectedSubtitle(subtitleId)
      console.log('选中的字幕轨道:', selectedTrack)

      // 首先检查字幕文件是否真的存在
      const checkSubtitleExists = async (src: string): Promise<boolean> => {
        try {
          const response = await fetch(src, { method: 'HEAD' })
          return response.ok
        } catch {
          return false
        }
      }

      let subtitleSrc = selectedTrack.src
      let needsGeneration = false

      // 如果没有src或者文件不存在，需要生成字幕
      if (!subtitleSrc) {
        needsGeneration = true
      } else {
        const exists = await checkSubtitleExists(subtitleSrc)
        if (!exists) {
          console.log('字幕文件不存在:', subtitleSrc)
          needsGeneration = true
        }
      }

      if (needsGeneration) {
        // 如果字幕文件不存在，生成字幕
        console.log('字幕文件不存在，开始生成:', selectedTrack.language)
        generateSubtitles(selectedTrack.language)
      } else {
        // 清除现有的字幕轨道元素
        const existingTracks = video.querySelectorAll('track')
        existingTracks.forEach(track => track.remove())

        // 创建新的字幕轨道元素
        const trackElement = document.createElement('track')
        trackElement.kind = 'subtitles'
        trackElement.src = subtitleSrc
        trackElement.srcLang = selectedTrack.language.split('-')[0]
        trackElement.label = selectedTrack.label
        trackElement.default = true

        console.log('添加字幕轨道:', {
          src: subtitleSrc,
          srcLang: selectedTrack.language.split('-')[0],
          label: selectedTrack.label
        })

        // 添加到video元素
        video.appendChild(trackElement)

        // 启用字幕显示
        setTimeout(() => {
          const textTracks = video.textTracks
          console.log('当前文本轨道数量:', textTracks.length)

          for (let i = 0; i < textTracks.length; i++) {
            const track = textTracks[i]
            console.log(`轨道 ${i}:`, {
              label: track.label,
              language: track.language,
              mode: track.mode,
              cues: track.cues?.length || 0
            })

            if (track.label === selectedTrack.label) {
              // 先禁用再启用，确保刷新
              track.mode = 'disabled'
              setTimeout(() => {
                track.mode = 'showing'
                console.log('成功启用字幕轨道:', track.label, '文件路径:', subtitleSrc)
                console.log('轨道状态:', {
                  mode: track.mode,
                  cues: track.cues?.length || 0,
                  activeCues: track.activeCues?.length || 0
                })
              }, 50)
              break
            }
          }
        }, 200)
      }
    }
    setShowSubtitleMenu(false)
  }

  const showControlsTemporarily = () => {
    setShowControls(true)
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current)
    }
    controlsTimeoutRef.current = setTimeout(() => {
      setShowControls(false)
    }, 3000)
  }

  // 视频加载完成处理
  const handleVideoLoaded = () => {
    const video = videoRef.current
    if (video) {
      setDuration(video.duration)
      setIsLoading(false)

      // 延迟初始化默认字幕，确保视频完全加载
      setTimeout(() => {
        if (selectedSubtitle !== 'off' && selectedSubtitle !== 'none') {
          console.log('视频加载完成，初始化默认字幕:', selectedSubtitle)
          handleSubtitleChange(selectedSubtitle)
        }
      }, 500)
    }
  }

  // 智能加载视频
  const loadVideo = () => {
    const video = videoRef.current
    if (video && video.readyState === 0) {
      setIsLoading(true)
      video.load()
    }
  }

  // 时间更新处理
  const handleTimeUpdate = () => {
    const video = videoRef.current
    if (video) {
      setCurrentTime(video.currentTime)
    }
  }

  // 时长变化处理
  const handleDurationChange = () => {
    const video = videoRef.current
    if (video) {
      setDuration(video.duration)
    }
  }

  // 视频错误处理
  const handleVideoError = (e: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    console.error('Video error:', e)
    const video = e.currentTarget
    if (video.error) {
      console.error('Video error details:', {
        code: video.error.code,
        message: video.error.message
      })
    }
  }

  // 构建认证的视频URL
  const getAuthenticatedVideoUrl = () => {
    try {
      const username = localStorage.getItem('admin_username') || 'admin'
      const password = localStorage.getItem('admin_password') || 'admin123'
      const credentials = btoa(`${username}:${password}`)
      const fileName = videoPath.split('/').pop()
      return `/api/video/${fileName}?auth=${encodeURIComponent(credentials)}&t=${Date.now()}`
    } catch (error) {
      console.error('Error generating video URL:', error)
      return ''
    }
  }

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* 调试信息 (开发模式) */}
      {process.env.NODE_ENV === 'development' && (
        <VideoDebugInfo
          videoRef={videoRef}
          videoPath={videoPath}
          selectedSubtitle={selectedSubtitle}
        />
      )}

      <div className={`relative w-full h-full ${isFullscreen ? '' : 'max-w-6xl max-h-full'}`}>
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center transition-colors"
        >
          <X className="text-white" size={24} />
        </button>

        {/* 视频标题 */}
        <div className="absolute top-4 left-4 z-10 bg-black/50 backdrop-blur-sm rounded px-4 py-2">
          <h2 className="text-white text-lg font-semibold">{title}</h2>
        </div>

        {/* 视频容器 */}
        <div
          className="relative w-full h-full cursor-pointer"
          onMouseMove={showControlsTemporarily}
          onClick={togglePlay}
        >
          {/* 视频预览/播放提示 */}
          {!isPlaying && currentTime === 0 && !isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900/90">
              <div className="text-center text-white">
                <div className="w-20 h-20 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
                  <Play size={40} className="ml-2" />
                </div>
                <h3 className="text-xl font-semibold mb-2">点击播放视频</h3>
                <p className="text-gray-300 text-sm">视频将在您点击时开始加载</p>
                <p className="text-gray-400 text-xs mt-2">这样可以节省系统资源</p>
              </div>
            </div>
          )}

          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black">
              <div className="text-white text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                <p>正在加载视频...</p>
                <p className="text-gray-400 text-sm mt-2">请稍候</p>
              </div>
            </div>
          )}

          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-black">
              <div className="text-white text-center">
                <p className="text-red-400 mb-4">{error}</p>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors"
                >
                  关闭
                </button>
              </div>
            </div>
          )}

          <video
            ref={videoRef}
            className="w-full h-full object-contain subtitle-video"
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
            onLoadedMetadata={handleVideoLoaded}
            onTimeUpdate={handleTimeUpdate}
            onDurationChange={handleDurationChange}
            onError={handleVideoError}
            onWaiting={() => setIsLoading(true)}
            onCanPlay={() => setIsLoading(false)}
            onCanPlayThrough={() => setIsLoading(false)}
            crossOrigin="anonymous"
            preload="none"
            playsInline
            controls={false}
            muted={volume === 0}
            style={{
              display: (!isPlaying && currentTime === 0) ? 'none' : 'block'
            } as React.CSSProperties}
          >
            {/* 字幕轨道将通过JavaScript动态添加 */}
          </video>

          {/* 播放控制栏 */}
          {showControls && !isLoading && !error && (
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
              {/* 进度条 */}
              <div className="mb-4">
                <input
                  type="range"
                  min="0"
                  max={duration || 0}
                  value={currentTime}
                  onChange={handleSeek}
                  className="w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* 控制按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      togglePlay()
                    }}
                    className="text-white hover:text-gray-300 transition-colors"
                  >
                    {isPlaying ? <Pause size={24} /> : <Play size={24} />}
                  </button>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleMute()
                      }}
                      className="text-white hover:text-gray-300 transition-colors"
                    >
                      {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                    </button>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={isMuted ? 0 : volume}
                      onChange={handleVolumeChange}
                      onClick={(e) => e.stopPropagation()}
                      className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                  </div>

                  <span className="text-white text-sm">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  {/* 字幕按钮 */}
                  <div className="relative">
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        setShowSubtitleMenu(!showSubtitleMenu)
                      }}
                      className={`text-white hover:text-gray-300 transition-colors ${
                        selectedSubtitle !== 'off' ? 'text-red-400' : ''
                      }`}
                      title="字幕设置"
                    >
                      <Subtitles size={20} />
                    </button>

                    {/* 字幕菜单 */}
                    {showSubtitleMenu && (
                      <div
                        className="absolute bottom-full right-0 mb-2 bg-black/90 backdrop-blur-sm rounded-lg p-2 min-w-[150px]"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="text-white text-sm font-semibold mb-2 px-2">字幕选择</div>

                        {/* 字幕轨道选项 */}
                        {subtitleTracks.map((track) => (
                          <button
                            key={track.id}
                            onClick={(e) => handleSubtitleChange(track.id, e)}
                            className={`w-full text-left px-2 py-1 text-sm rounded transition-colors flex items-center justify-between ${
                              selectedSubtitle === track.id
                                ? 'bg-red-600 text-white'
                                : 'text-gray-300 hover:bg-gray-700'
                            }`}
                          >
                            <span>{track.label}</span>
                            {!track.src && (
                              <span className="text-xs text-gray-400">
                                {isGeneratingSubtitles ? '生成中...' : '生成'}
                              </span>
                            )}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 调试信息按钮 */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      setShowDebugInfo(!showDebugInfo)
                    }}
                    className={`text-white hover:text-gray-300 transition-colors ${showDebugInfo ? 'text-yellow-400' : ''}`}
                    title="调试信息"
                  >
                    <Settings size={20} />
                  </button>

                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleFullscreen()
                    }}
                    className="text-white hover:text-gray-300 transition-colors"
                  >
                    <Maximize size={20} />
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* 调试信息面板 */}
          {showDebugInfo && (
            <div className="absolute top-20 left-4 bg-black/90 backdrop-blur-sm rounded-lg p-4 text-white text-sm max-w-md">
              <h3 className="font-semibold mb-2">字幕调试信息</h3>
              <div className="space-y-1">
                <p>当前选择: {selectedSubtitle}</p>
                <p>视频路径: {videoPath}</p>
                <p>字幕轨道数量: {subtitleTracks.length}</p>
                <p>视频文本轨道数量: {videoRef.current?.textTracks.length || 0}</p>

                <div className="mt-2">
                  <p className="font-semibold">可用字幕轨道:</p>
                  {subtitleTracks.map(track => (
                    <div key={track.id} className="ml-2 text-xs">
                      <p>{track.label} ({track.id})</p>
                      <p className="text-gray-400">语言: {track.language}</p>
                      <p className="text-gray-400">文件: {track.src || '无'}</p>
                    </div>
                  ))}
                </div>

                <div className="mt-2">
                  <p className="font-semibold">视频文本轨道状态:</p>
                  {videoRef.current && Array.from(videoRef.current.textTracks).map((track, index) => (
                    <div key={index} className="ml-2 text-xs">
                      <p>轨道 {index}: {track.label}</p>
                      <p className="text-gray-400">模式: {track.mode}</p>
                      <p className="text-gray-400">语言: {track.language}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
