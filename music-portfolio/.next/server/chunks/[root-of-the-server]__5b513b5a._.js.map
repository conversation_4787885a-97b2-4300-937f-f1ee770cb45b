{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/subtitles/%5Bfilename%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { readFile } from 'fs/promises'\nimport { join } from 'path'\nimport { existsSync } from 'fs'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { filename: string } }\n) {\n  try {\n    const filename = params.filename\n    \n    // 验证文件名格式\n    if (!filename.endsWith('.vtt')) {\n      return NextResponse.json(\n        { error: '无效的字幕文件格式' },\n        { status: 400 }\n      )\n    }\n\n    // 构建文件路径\n    const subtitlePath = join(process.cwd(), 'public', 'subtitles', filename)\n    \n    // 检查文件是否存在\n    if (!existsSync(subtitlePath)) {\n      return NextResponse.json(\n        { error: '字幕文件不存在' },\n        { status: 404 }\n      )\n    }\n\n    // 读取文件内容\n    const content = await readFile(subtitlePath, 'utf-8')\n\n    // 返回字幕文件，设置正确的MIME类型和CORS头\n    return new NextResponse(content, {\n      status: 200,\n      headers: {\n        'Content-Type': 'text/vtt; charset=utf-8',\n        'Access-Control-Allow-Origin': '*',\n        'Access-Control-Allow-Methods': 'GET',\n        'Access-Control-Allow-Headers': 'Content-Type',\n        'Cache-Control': 'public, max-age=3600'\n      }\n    })\n\n  } catch (error) {\n    console.error('字幕文件读取失败:', error)\n    return NextResponse.json(\n      { error: '字幕文件读取失败' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function OPTIONS() {\n  return new NextResponse(null, {\n    status: 200,\n    headers: {\n      'Access-Control-Allow-Origin': '*',\n      'Access-Control-Allow-Methods': 'GET',\n      'Access-Control-Allow-Headers': 'Content-Type',\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAoC;IAE5C,IAAI;QACF,MAAM,WAAW,OAAO,QAAQ;QAEhC,UAAU;QACV,IAAI,CAAC,SAAS,QAAQ,CAAC,SAAS;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAY,GACrB;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,eAAe,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU,aAAa;QAEhE,WAAW;QACX,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,eAAe;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAU,GACnB;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,UAAU,MAAM,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;QAE7C,2BAA2B;QAC3B,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,SAAS;YAC/B,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,+BAA+B;gBAC/B,gCAAgC;gBAChC,gCAAgC;gBAChC,iBAAiB;YACnB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAW,GACpB;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,QAAQ;QACR,SAAS;YACP,+BAA+B;YAC/B,gCAAgC;YAChC,gCAAgC;QAClC;IACF;AACF", "debugId": null}}]}