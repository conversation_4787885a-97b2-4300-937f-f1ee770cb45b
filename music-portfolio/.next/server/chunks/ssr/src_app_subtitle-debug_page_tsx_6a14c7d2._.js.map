{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/subtitle-debug/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\n\nexport default function SubtitleDebugPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [selectedSubtitle, setSelectedSubtitle] = useState('zh')\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [debugInfo, setDebugInfo] = useState<string[]>([])\n  const [textTracks, setTextTracks] = useState<any[]>([])\n\n  const addDebugInfo = (message: string) => {\n    const timestamp = new Date().toLocaleTimeString()\n    setDebugInfo(prev => [...prev, `[${timestamp}] ${message}`])\n  }\n\n  const subtitleTracks = [\n    {\n      id: 'none',\n      label: '无字幕',\n      language: 'none',\n      src: ''\n    },\n    {\n      id: 'zh',\n      label: '中文字幕',\n      language: 'zh',\n      src: '/subtitles/default_zh.vtt'\n    },\n    {\n      id: 'zh-ko',\n      label: '中韩字幕 (中文+韩文)',\n      language: 'zh-ko',\n      src: '/subtitles/default_zh-ko.vtt'\n    },\n    {\n      id: 'zh-en',\n      label: '中英字幕 (中文+英文)',\n      language: 'zh-en',\n      src: '/subtitles/default_zh-en.vtt'\n    }\n  ]\n\n  const updateTextTracksInfo = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    const tracks = Array.from(video.textTracks).map((track, index) => ({\n      index,\n      kind: track.kind,\n      label: track.label,\n      language: track.language,\n      mode: track.mode,\n      cues: track.cues ? track.cues.length : 0\n    }))\n    setTextTracks(tracks)\n  }\n\n  const handleSubtitleChange = async (subtitleId: string) => {\n    const video = videoRef.current\n    if (!video) return\n\n    addDebugInfo(`开始切换字幕: ${subtitleId}`)\n\n    // 禁用所有现有字幕轨道\n    const tracks = video.textTracks\n    for (let i = 0; i < tracks.length; i++) {\n      tracks[i].mode = 'disabled'\n      addDebugInfo(`禁用轨道 ${i}: ${tracks[i].label}`)\n    }\n\n    if (subtitleId === 'none') {\n      setSelectedSubtitle('none')\n      addDebugInfo('选择无字幕')\n      updateTextTracksInfo()\n      return\n    }\n\n    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)\n    if (selectedTrack && selectedTrack.src) {\n      setSelectedSubtitle(subtitleId)\n      addDebugInfo(`选中字幕轨道: ${selectedTrack.label}`)\n      addDebugInfo(`字幕文件路径: ${selectedTrack.src}`)\n\n      // 清除现有的字幕轨道元素\n      const existingTracks = video.querySelectorAll('track')\n      addDebugInfo(`清除现有轨道元素: ${existingTracks.length} 个`)\n      existingTracks.forEach((track, index) => {\n        addDebugInfo(`移除轨道元素 ${index}: ${track.label}`)\n        track.remove()\n      })\n\n      // 创建新的字幕轨道元素\n      const trackElement = document.createElement('track')\n      trackElement.kind = 'subtitles'\n      trackElement.src = selectedTrack.src\n      trackElement.srcLang = selectedTrack.language.split('-')[0]\n      trackElement.label = selectedTrack.label\n      trackElement.default = true\n\n      addDebugInfo(`创建新轨道元素: ${trackElement.label}`)\n      addDebugInfo(`轨道语言: ${trackElement.srcLang}`)\n\n      // 添加事件监听器\n      trackElement.addEventListener('load', () => {\n        addDebugInfo(`字幕文件加载成功: ${selectedTrack.src}`)\n        trackElement.track.mode = 'showing'\n        addDebugInfo(`设置轨道模式为 showing`)\n        updateTextTracksInfo()\n      })\n\n      trackElement.addEventListener('error', (e) => {\n        addDebugInfo(`字幕文件加载失败: ${e}`)\n      })\n\n      // 添加到video元素\n      video.appendChild(trackElement)\n      addDebugInfo(`轨道元素已添加到视频`)\n\n      // 延迟启用字幕显示\n      setTimeout(() => {\n        const textTracks = video.textTracks\n        addDebugInfo(`当前文本轨道数量: ${textTracks.length}`)\n        \n        for (let i = 0; i < textTracks.length; i++) {\n          const track = textTracks[i]\n          addDebugInfo(`轨道 ${i}: ${track.label}, 模式: ${track.mode}, 提示数: ${track.cues ? track.cues.length : 0}`)\n          \n          if (track.label === selectedTrack.label) {\n            track.mode = 'showing'\n            addDebugInfo(`成功启用字幕轨道: ${track.label}`)\n            \n            // 检查字幕内容\n            if (track.cues && track.cues.length > 0) {\n              addDebugInfo(`字幕提示数量: ${track.cues.length}`)\n              const firstCue = track.cues[0]\n              addDebugInfo(`第一个字幕: \"${firstCue.text}\"`)\n            }\n            break\n          }\n        }\n        updateTextTracksInfo()\n      }, 500)\n    }\n  }\n\n  const clearDebugInfo = () => {\n    setDebugInfo([])\n  }\n\n  const testSubtitleFile = async (src: string) => {\n    try {\n      const response = await fetch(src)\n      if (response.ok) {\n        const content = await response.text()\n        addDebugInfo(`字幕文件 ${src} 可访问，内容长度: ${content.length}`)\n        addDebugInfo(`文件内容预览: ${content.substring(0, 100)}...`)\n      } else {\n        addDebugInfo(`字幕文件 ${src} 访问失败: ${response.status}`)\n      }\n    } catch (error) {\n      addDebugInfo(`字幕文件 ${src} 测试失败: ${error}`)\n    }\n  }\n\n  useEffect(() => {\n    // 初始化\n    addDebugInfo('页面初始化')\n    updateTextTracksInfo()\n    \n    // 测试所有字幕文件\n    subtitleTracks.forEach(track => {\n      if (track.src) {\n        testSubtitleFile(track.src)\n      }\n    })\n  }, [])\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-4\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-6\">字幕调试工具</h1>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* 视频播放器 */}\n          <div className=\"bg-gray-800 rounded-lg p-4\">\n            <h2 className=\"text-xl font-semibold text-white mb-4\">视频播放器</h2>\n            <div className=\"bg-black rounded-lg overflow-hidden mb-4\">\n              <video\n                ref={videoRef}\n                className=\"w-full h-auto\"\n                onPlay={() => {\n                  setIsPlaying(true)\n                  addDebugInfo('视频开始播放')\n                }}\n                onPause={() => {\n                  setIsPlaying(false)\n                  addDebugInfo('视频暂停')\n                }}\n                onLoadedMetadata={() => {\n                  addDebugInfo('视频元数据加载完成')\n                  updateTextTracksInfo()\n                }}\n                crossOrigin=\"anonymous\"\n                controls\n              >\n                <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n                您的浏览器不支持视频播放。\n              </video>\n            </div>\n\n            {/* 字幕选择 */}\n            <div className=\"grid grid-cols-2 gap-2 mb-4\">\n              {subtitleTracks.map((track) => (\n                <button\n                  key={track.id}\n                  onClick={() => handleSubtitleChange(track.id)}\n                  className={`p-2 rounded text-sm font-medium transition-colors ${\n                    selectedSubtitle === track.id\n                      ? 'bg-red-600 text-white'\n                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                  }`}\n                >\n                  {track.label}\n                </button>\n              ))}\n            </div>\n\n            {/* 当前状态 */}\n            <div className=\"text-gray-300 text-sm\">\n              <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>\n              <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>\n            </div>\n          </div>\n\n          {/* 调试信息 */}\n          <div className=\"bg-gray-800 rounded-lg p-4\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-xl font-semibold text-white\">调试信息</h2>\n              <button\n                onClick={clearDebugInfo}\n                className=\"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700\"\n              >\n                清除\n              </button>\n            </div>\n            <div className=\"bg-gray-900 rounded p-3 h-64 overflow-y-auto\">\n              {debugInfo.map((info, index) => (\n                <div key={index} className=\"text-green-400 text-xs mb-1 font-mono\">\n                  {info}\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* 文本轨道信息 */}\n        <div className=\"bg-gray-800 rounded-lg p-4 mt-6\">\n          <h2 className=\"text-xl font-semibold text-white mb-4\">当前文本轨道</h2>\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full text-sm text-gray-300\">\n              <thead>\n                <tr className=\"border-b border-gray-600\">\n                  <th className=\"text-left p-2\">索引</th>\n                  <th className=\"text-left p-2\">类型</th>\n                  <th className=\"text-left p-2\">标签</th>\n                  <th className=\"text-left p-2\">语言</th>\n                  <th className=\"text-left p-2\">模式</th>\n                  <th className=\"text-left p-2\">提示数</th>\n                </tr>\n              </thead>\n              <tbody>\n                {textTracks.map((track) => (\n                  <tr key={track.index} className=\"border-b border-gray-700\">\n                    <td className=\"p-2\">{track.index}</td>\n                    <td className=\"p-2\">{track.kind}</td>\n                    <td className=\"p-2\">{track.label}</td>\n                    <td className=\"p-2\">{track.language}</td>\n                    <td className=\"p-2\">\n                      <span className={`px-2 py-1 rounded text-xs ${\n                        track.mode === 'showing' ? 'bg-green-600' : \n                        track.mode === 'disabled' ? 'bg-red-600' : 'bg-gray-600'\n                      }`}>\n                        {track.mode}\n                      </span>\n                    </td>\n                    <td className=\"p-2\">{track.cues}</td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEtD,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY,IAAI,OAAO,kBAAkB;QAC/C,aAAa,CAAA,OAAQ;mBAAI;gBAAM,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,SAAS;aAAC;IAC7D;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;KACD;IAED,MAAM,uBAAuB;QAC3B,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,UAAU,EAAE,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;gBACjE;gBACA,MAAM,MAAM,IAAI;gBAChB,OAAO,MAAM,KAAK;gBAClB,UAAU,MAAM,QAAQ;gBACxB,MAAM,MAAM,IAAI;gBAChB,MAAM,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,GAAG;YACzC,CAAC;QACD,cAAc;IAChB;IAEA,MAAM,uBAAuB,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,aAAa,CAAC,QAAQ,EAAE,YAAY;QAEpC,aAAa;QACb,MAAM,SAAS,MAAM,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;YACjB,aAAa,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE;QAC9C;QAEA,IAAI,eAAe,QAAQ;YACzB,oBAAoB;YACpB,aAAa;YACb;YACA;QACF;QAEA,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChE,IAAI,iBAAiB,cAAc,GAAG,EAAE;YACtC,oBAAoB;YACpB,aAAa,CAAC,QAAQ,EAAE,cAAc,KAAK,EAAE;YAC7C,aAAa,CAAC,QAAQ,EAAE,cAAc,GAAG,EAAE;YAE3C,cAAc;YACd,MAAM,iBAAiB,MAAM,gBAAgB,CAAC;YAC9C,aAAa,CAAC,UAAU,EAAE,eAAe,MAAM,CAAC,EAAE,CAAC;YACnD,eAAe,OAAO,CAAC,CAAC,OAAO;gBAC7B,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,MAAM,KAAK,EAAE;gBAC9C,MAAM,MAAM;YACd;YAEA,aAAa;YACb,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,IAAI,GAAG;YACpB,aAAa,GAAG,GAAG,cAAc,GAAG;YACpC,aAAa,OAAO,GAAG,cAAc,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3D,aAAa,KAAK,GAAG,cAAc,KAAK;YACxC,aAAa,OAAO,GAAG;YAEvB,aAAa,CAAC,SAAS,EAAE,aAAa,KAAK,EAAE;YAC7C,aAAa,CAAC,MAAM,EAAE,aAAa,OAAO,EAAE;YAE5C,UAAU;YACV,aAAa,gBAAgB,CAAC,QAAQ;gBACpC,aAAa,CAAC,UAAU,EAAE,cAAc,GAAG,EAAE;gBAC7C,aAAa,KAAK,CAAC,IAAI,GAAG;gBAC1B,aAAa,CAAC,eAAe,CAAC;gBAC9B;YACF;YAEA,aAAa,gBAAgB,CAAC,SAAS,CAAC;gBACtC,aAAa,CAAC,UAAU,EAAE,GAAG;YAC/B;YAEA,aAAa;YACb,MAAM,WAAW,CAAC;YAClB,aAAa,CAAC,UAAU,CAAC;YAEzB,WAAW;YACX,WAAW;gBACT,MAAM,aAAa,MAAM,UAAU;gBACnC,aAAa,CAAC,UAAU,EAAE,WAAW,MAAM,EAAE;gBAE7C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;oBAC1C,MAAM,QAAQ,UAAU,CAAC,EAAE;oBAC3B,aAAa,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,MAAM,KAAK,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG;oBAErG,IAAI,MAAM,KAAK,KAAK,cAAc,KAAK,EAAE;wBACvC,MAAM,IAAI,GAAG;wBACb,aAAa,CAAC,UAAU,EAAE,MAAM,KAAK,EAAE;wBAEvC,SAAS;wBACT,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG;4BACvC,aAAa,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE;4BAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE;4BAC9B,aAAa,CAAC,QAAQ,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;wBAC1C;wBACA;oBACF;gBACF;gBACA;YACF,GAAG;QACL;IACF;IAEA,MAAM,iBAAiB;QACrB,aAAa,EAAE;IACjB;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,UAAU,MAAM,SAAS,IAAI;gBACnC,aAAa,CAAC,KAAK,EAAE,IAAI,WAAW,EAAE,QAAQ,MAAM,EAAE;gBACtD,aAAa,CAAC,QAAQ,EAAE,QAAQ,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC;YACxD,OAAO;gBACL,aAAa,CAAC,KAAK,EAAE,IAAI,OAAO,EAAE,SAAS,MAAM,EAAE;YACrD;QACF,EAAE,OAAO,OAAO;YACd,aAAa,CAAC,KAAK,EAAE,IAAI,OAAO,EAAE,OAAO;QAC3C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM;QACN,aAAa;QACb;QAEA,WAAW;QACX,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,MAAM,GAAG,EAAE;gBACb,iBAAiB,MAAM,GAAG;YAC5B;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAK;wCACL,WAAU;wCACV,QAAQ;4CACN,aAAa;4CACb,aAAa;wCACf;wCACA,SAAS;4CACP,aAAa;4CACb,aAAa;wCACf;wCACA,kBAAkB;4CAChB,aAAa;4CACb;wCACF;wCACA,aAAY;wCACZ,QAAQ;;0DAER,8OAAC;gDAAO,KAAI;gDAA0C,MAAK;;;;;;4CAAc;;;;;;;;;;;;8CAM7E,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;4CAEC,SAAS,IAAM,qBAAqB,MAAM,EAAE;4CAC5C,WAAW,CAAC,kDAAkD,EAC5D,qBAAqB,MAAM,EAAE,GACzB,0BACA,+CACJ;sDAED,MAAM,KAAK;2CARP,MAAM,EAAE;;;;;;;;;;8CAcnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAO,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB;;;;;;;sDAC/D,8OAAC;;gDAAE;gDAAO,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;sCAKlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAIH,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;4CAAgB,WAAU;sDACxB;2CADO;;;;;;;;;;;;;;;;;;;;;;8BASlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;kDACC,cAAA,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;8DAC9B,8OAAC;oDAAG,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAGlC,8OAAC;kDACE,WAAW,GAAG,CAAC,CAAC,sBACf,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAG,WAAU;kEAAO,MAAM,KAAK;;;;;;kEAChC,8OAAC;wDAAG,WAAU;kEAAO,MAAM,IAAI;;;;;;kEAC/B,8OAAC;wDAAG,WAAU;kEAAO,MAAM,KAAK;;;;;;kEAChC,8OAAC;wDAAG,WAAU;kEAAO,MAAM,QAAQ;;;;;;kEACnC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAK,WAAW,CAAC,0BAA0B,EAC1C,MAAM,IAAI,KAAK,YAAY,iBAC3B,MAAM,IAAI,KAAK,aAAa,eAAe,eAC3C;sEACC,MAAM,IAAI;;;;;;;;;;;kEAGf,8OAAC;wDAAG,WAAU;kEAAO,MAAM,IAAI;;;;;;;+CAbxB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBtC", "debugId": null}}]}