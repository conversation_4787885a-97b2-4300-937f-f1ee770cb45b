{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/subtitle-diagnosis/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useRef, useEffect, useState } from 'react'\n\nexport default function SubtitleDiagnosisPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [diagnostics, setDiagnostics] = useState<any>({})\n  const [currentTime, setCurrentTime] = useState(0)\n\n  useEffect(() => {\n    const video = videoRef.current\n    if (!video) return\n\n    const runDiagnostics = () => {\n      const tracks = Array.from(video.textTracks)\n      const videoRect = video.getBoundingClientRect()\n      \n      const diag = {\n        videoElement: {\n          src: video.src,\n          readyState: video.readyState,\n          currentTime: video.currentTime,\n          duration: video.duration,\n          videoWidth: video.videoWidth,\n          videoHeight: video.videoHeight,\n          clientWidth: video.clientWidth,\n          clientHeight: video.clientHeight,\n          crossOrigin: video.crossOrigin\n        },\n        textTracks: tracks.map((track, index) => ({\n          index,\n          kind: track.kind,\n          label: track.label,\n          language: track.language,\n          mode: track.mode,\n          cues: track.cues ? Array.from(track.cues).map(cue => ({\n            startTime: cue.startTime,\n            endTime: cue.endTime,\n            text: cue.text,\n            id: cue.id\n          })) : [],\n          activeCues: track.activeCues ? Array.from(track.activeCues).map(cue => ({\n            startTime: cue.startTime,\n            endTime: cue.endTime,\n            text: cue.text\n          })) : []\n        })),\n        browser: {\n          userAgent: navigator.userAgent,\n          platform: navigator.platform,\n          language: navigator.language\n        },\n        styles: {\n          computedStyle: window.getComputedStyle(video),\n          videoRect: {\n            width: videoRect.width,\n            height: videoRect.height,\n            top: videoRect.top,\n            left: videoRect.left\n          }\n        }\n      }\n      \n      setDiagnostics(diag)\n    }\n\n    const handleTimeUpdate = () => {\n      setCurrentTime(video.currentTime)\n      runDiagnostics()\n    }\n\n    video.addEventListener('loadedmetadata', runDiagnostics)\n    video.addEventListener('timeupdate', handleTimeUpdate)\n    video.addEventListener('loadstart', runDiagnostics)\n    video.addEventListener('canplay', runDiagnostics)\n\n    // 强制启用所有字幕轨道\n    const enableAllTracks = () => {\n      const tracks = video.textTracks\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'showing'\n        console.log(`启用轨道 ${i}:`, tracks[i])\n      }\n      runDiagnostics()\n    }\n\n    setTimeout(enableAllTracks, 2000)\n\n    return () => {\n      video.removeEventListener('loadedmetadata', runDiagnostics)\n      video.removeEventListener('timeupdate', handleTimeUpdate)\n      video.removeEventListener('loadstart', runDiagnostics)\n      video.removeEventListener('canplay', runDiagnostics)\n    }\n  }, [])\n\n  const testSubtitleFile = async (url: string) => {\n    try {\n      const response = await fetch(url)\n      const text = await response.text()\n      console.log('字幕文件内容:', text)\n      return { success: true, content: text.substring(0, 500) }\n    } catch (error) {\n      console.error('字幕文件加载失败:', error)\n      return { success: false, error: String(error) }\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">字幕问题诊断页面</h1>\n        \n        <div className=\"space-y-6\">\n          {/* 视频播放器 */}\n          <div className=\"bg-black rounded-lg overflow-hidden\">\n            <video\n              ref={videoRef}\n              className=\"w-full h-auto\"\n              controls\n              crossOrigin=\"anonymous\"\n              autoPlay={false}\n            >\n              <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n              <track\n                kind=\"subtitles\"\n                src=\"/api/subtitles/test_simple.vtt\"\n                srcLang=\"zh\"\n                label=\"测试字幕\"\n                default\n              />\n            </video>\n          </div>\n\n          {/* 控制按钮 */}\n          <div className=\"flex space-x-4\">\n            <button\n              onClick={() => testSubtitleFile('/api/subtitles/test_simple.vtt')}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n            >\n              测试字幕文件\n            </button>\n            <button\n              onClick={() => {\n                const video = videoRef.current\n                if (video) {\n                  for (let i = 0; i < video.textTracks.length; i++) {\n                    video.textTracks[i].mode = 'showing'\n                  }\n                }\n              }}\n              className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n            >\n              强制启用字幕\n            </button>\n          </div>\n\n          {/* 诊断信息 */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* 视频信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">视频元素信息</h2>\n              {diagnostics.videoElement && (\n                <div className=\"text-sm text-gray-300 space-y-1\">\n                  <p>源文件: {diagnostics.videoElement.src}</p>\n                  <p>就绪状态: {diagnostics.videoElement.readyState}</p>\n                  <p>当前时间: {diagnostics.videoElement.currentTime?.toFixed(2)}s</p>\n                  <p>总时长: {diagnostics.videoElement.duration?.toFixed(2)}s</p>\n                  <p>视频尺寸: {diagnostics.videoElement.videoWidth} x {diagnostics.videoElement.videoHeight}</p>\n                  <p>显示尺寸: {diagnostics.videoElement.clientWidth} x {diagnostics.videoElement.clientHeight}</p>\n                  <p>跨域设置: {diagnostics.videoElement.crossOrigin || '无'}</p>\n                </div>\n              )}\n            </div>\n\n            {/* 字幕轨道信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">字幕轨道信息</h2>\n              {diagnostics.textTracks && diagnostics.textTracks.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {diagnostics.textTracks.map((track: any) => (\n                    <div key={track.index} className=\"bg-gray-700 rounded p-3\">\n                      <h3 className=\"font-medium text-white\">轨道 {track.index}: {track.label}</h3>\n                      <div className=\"text-xs text-gray-300 mt-2 space-y-1\">\n                        <p>类型: {track.kind}</p>\n                        <p>语言: {track.language}</p>\n                        <p>模式: <span className={track.mode === 'showing' ? 'text-green-400' : 'text-red-400'}>{track.mode}</span></p>\n                        <p>字幕条目数: {track.cues.length}</p>\n                        <p>活跃字幕数: {track.activeCues.length}</p>\n                        \n                        {track.activeCues.length > 0 && (\n                          <div className=\"mt-2 p-2 bg-gray-600 rounded\">\n                            <p className=\"text-yellow-400 font-medium\">当前活跃字幕:</p>\n                            {track.activeCues.map((cue: any, index: number) => (\n                              <p key={index} className=\"text-white whitespace-pre-line\">{cue.text}</p>\n                            ))}\n                          </div>\n                        )}\n                        \n                        {track.cues.length > 0 && (\n                          <details className=\"mt-2\">\n                            <summary className=\"text-blue-400 cursor-pointer\">查看所有字幕条目</summary>\n                            <div className=\"mt-2 max-h-40 overflow-y-auto\">\n                              {track.cues.map((cue: any, index: number) => (\n                                <div key={index} className=\"text-xs p-1 border-b border-gray-600\">\n                                  <p>{cue.startTime.toFixed(2)}s - {cue.endTime.toFixed(2)}s</p>\n                                  <p className=\"text-gray-200\">{cue.text}</p>\n                                </div>\n                              ))}\n                            </div>\n                          </details>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-400\">没有检测到字幕轨道</p>\n              )}\n            </div>\n\n            {/* 浏览器信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">浏览器信息</h2>\n              {diagnostics.browser && (\n                <div className=\"text-sm text-gray-300 space-y-1\">\n                  <p>用户代理: {diagnostics.browser.userAgent}</p>\n                  <p>平台: {diagnostics.browser.platform}</p>\n                  <p>语言: {diagnostics.browser.language}</p>\n                </div>\n              )}\n            </div>\n\n            {/* 样式信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">样式信息</h2>\n              {diagnostics.styles && (\n                <div className=\"text-sm text-gray-300 space-y-1\">\n                  <p>视频位置: {diagnostics.styles.videoRect?.left}, {diagnostics.styles.videoRect?.top}</p>\n                  <p>视频大小: {diagnostics.styles.videoRect?.width} x {diagnostics.styles.videoRect?.height}</p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 说明 */}\n          <div className=\"bg-gray-800 rounded-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-white mb-4\">诊断说明</h2>\n            <div className=\"text-gray-300 text-sm space-y-2\">\n              <p>1. 检查视频元素是否正确加载</p>\n              <p>2. 检查字幕轨道是否正确添加和启用</p>\n              <p>3. 检查字幕条目是否正确解析</p>\n              <p>4. 检查当前时间是否有活跃的字幕</p>\n              <p>5. 如果有活跃字幕但视频上不显示，可能是CSS样式问题</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,iBAAiB;YACrB,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,UAAU;YAC1C,MAAM,YAAY,MAAM,qBAAqB;YAE7C,MAAM,OAAO;gBACX,cAAc;oBACZ,KAAK,MAAM,GAAG;oBACd,YAAY,MAAM,UAAU;oBAC5B,aAAa,MAAM,WAAW;oBAC9B,UAAU,MAAM,QAAQ;oBACxB,YAAY,MAAM,UAAU;oBAC5B,aAAa,MAAM,WAAW;oBAC9B,aAAa,MAAM,WAAW;oBAC9B,cAAc,MAAM,YAAY;oBAChC,aAAa,MAAM,WAAW;gBAChC;gBACA,YAAY,OAAO,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;wBACxC;wBACA,MAAM,MAAM,IAAI;wBAChB,OAAO,MAAM,KAAK;wBAClB,UAAU,MAAM,QAAQ;wBACxB,MAAM,MAAM,IAAI;wBAChB,MAAM,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gCACpD,WAAW,IAAI,SAAS;gCACxB,SAAS,IAAI,OAAO;gCACpB,MAAM,IAAI,IAAI;gCACd,IAAI,IAAI,EAAE;4BACZ,CAAC,KAAK,EAAE;wBACR,YAAY,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,UAAU,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gCACtE,WAAW,IAAI,SAAS;gCACxB,SAAS,IAAI,OAAO;gCACpB,MAAM,IAAI,IAAI;4BAChB,CAAC,KAAK,EAAE;oBACV,CAAC;gBACD,SAAS;oBACP,WAAW,UAAU,SAAS;oBAC9B,UAAU,UAAU,QAAQ;oBAC5B,UAAU,UAAU,QAAQ;gBAC9B;gBACA,QAAQ;oBACN,eAAe,OAAO,gBAAgB,CAAC;oBACvC,WAAW;wBACT,OAAO,UAAU,KAAK;wBACtB,QAAQ,UAAU,MAAM;wBACxB,KAAK,UAAU,GAAG;wBAClB,MAAM,UAAU,IAAI;oBACtB;gBACF;YACF;YAEA,eAAe;QACjB;QAEA,MAAM,mBAAmB;YACvB,eAAe,MAAM,WAAW;YAChC;QACF;QAEA,MAAM,gBAAgB,CAAC,kBAAkB;QACzC,MAAM,gBAAgB,CAAC,cAAc;QACrC,MAAM,gBAAgB,CAAC,aAAa;QACpC,MAAM,gBAAgB,CAAC,WAAW;QAElC,aAAa;QACb,MAAM,kBAAkB;YACtB,MAAM,SAAS,MAAM,UAAU;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;gBACjB,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;YACrC;YACA;QACF;QAEA,WAAW,iBAAiB;QAE5B,OAAO;YACL,MAAM,mBAAmB,CAAC,kBAAkB;YAC5C,MAAM,mBAAmB,CAAC,cAAc;YACxC,MAAM,mBAAmB,CAAC,aAAa;YACvC,MAAM,mBAAmB,CAAC,WAAW;QACvC;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,WAAW;YACvB,OAAO;gBAAE,SAAS;gBAAM,SAAS,KAAK,SAAS,CAAC,GAAG;YAAK;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBAAE,SAAS;gBAAO,OAAO,OAAO;YAAO;QAChD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,KAAK;gCACL,WAAU;gCACV,QAAQ;gCACR,aAAY;gCACZ,UAAU;;kDAEV,8OAAC;wCAAO,KAAI;wCAA0C,MAAK;;;;;;kDAC3D,8OAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,SAAQ;wCACR,OAAM;wCACN,OAAO;;;;;;;;;;;;;;;;;sCAMb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;wCACP,MAAM,QAAQ,SAAS,OAAO;wCAC9B,IAAI,OAAO;4CACT,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,UAAU,CAAC,MAAM,EAAE,IAAK;gDAChD,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG;4CAC7B;wCACF;oCACF;oCACA,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,YAAY,kBACvB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;wDAAM,YAAY,YAAY,CAAC,GAAG;;;;;;;8DACrC,8OAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,UAAU;;;;;;;8DAC7C,8OAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,WAAW,EAAE,QAAQ;wDAAG;;;;;;;8DAC3D,8OAAC;;wDAAE;wDAAM,YAAY,YAAY,CAAC,QAAQ,EAAE,QAAQ;wDAAG;;;;;;;8DACvD,8OAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,UAAU;wDAAC;wDAAI,YAAY,YAAY,CAAC,WAAW;;;;;;;8DACtF,8OAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,WAAW;wDAAC;wDAAI,YAAY,YAAY,CAAC,YAAY;;;;;;;8DACxF,8OAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,WAAW,IAAI;;;;;;;;;;;;;;;;;;;8CAMxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,UAAU,IAAI,YAAY,UAAU,CAAC,MAAM,GAAG,kBACzD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,sBAC3B,8OAAC;oDAAsB,WAAU;;sEAC/B,8OAAC;4DAAG,WAAU;;gEAAyB;gEAAI,MAAM,KAAK;gEAAC;gEAAG,MAAM,KAAK;;;;;;;sEACrE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAE;wEAAK,MAAM,IAAI;;;;;;;8EAClB,8OAAC;;wEAAE;wEAAK,MAAM,QAAQ;;;;;;;8EACtB,8OAAC;;wEAAE;sFAAI,8OAAC;4EAAK,WAAW,MAAM,IAAI,KAAK,YAAY,mBAAmB;sFAAiB,MAAM,IAAI;;;;;;;;;;;;8EACjG,8OAAC;;wEAAE;wEAAQ,MAAM,IAAI,CAAC,MAAM;;;;;;;8EAC5B,8OAAC;;wEAAE;wEAAQ,MAAM,UAAU,CAAC,MAAM;;;;;;;gEAEjC,MAAM,UAAU,CAAC,MAAM,GAAG,mBACzB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAA8B;;;;;;wEAC1C,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,KAAU,sBAC/B,8OAAC;gFAAc,WAAU;0FAAkC,IAAI,IAAI;+EAA3D;;;;;;;;;;;gEAKb,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,8OAAC;oEAAQ,WAAU;;sFACjB,8OAAC;4EAAQ,WAAU;sFAA+B;;;;;;sFAClD,8OAAC;4EAAI,WAAU;sFACZ,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,sBACzB,8OAAC;oFAAgB,WAAU;;sGACzB,8OAAC;;gGAAG,IAAI,SAAS,CAAC,OAAO,CAAC;gGAAG;gGAAK,IAAI,OAAO,CAAC,OAAO,CAAC;gGAAG;;;;;;;sGACzD,8OAAC;4FAAE,WAAU;sGAAiB,IAAI,IAAI;;;;;;;mFAF9B;;;;;;;;;;;;;;;;;;;;;;;mDAvBZ,MAAM,KAAK;;;;;;;;;iEAoCzB,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAKjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,OAAO,kBAClB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;wDAAO,YAAY,OAAO,CAAC,SAAS;;;;;;;8DACvC,8OAAC;;wDAAE;wDAAK,YAAY,OAAO,CAAC,QAAQ;;;;;;;8DACpC,8OAAC;;wDAAE;wDAAK,YAAY,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;8CAM1C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,MAAM,kBACjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;wDAAO,YAAY,MAAM,CAAC,SAAS,EAAE;wDAAK;wDAAG,YAAY,MAAM,CAAC,SAAS,EAAE;;;;;;;8DAC9E,8OAAC;;wDAAE;wDAAO,YAAY,MAAM,CAAC,SAAS,EAAE;wDAAM;wDAAI,YAAY,MAAM,CAAC,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}