{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/subtitle-display-test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\n\nexport default function SubtitleDisplayTestPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [selectedSubtitle, setSelectedSubtitle] = useState('zh-ko')\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [debugInfo, setDebugInfo] = useState<string[]>([])\n  const [currentTime, setCurrentTime] = useState(0)\n\n  const addDebugInfo = (info: string) => {\n    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`])\n  }\n\n  const subtitleTracks = [\n    {\n      id: 'none',\n      label: '无字幕',\n      src: ''\n    },\n    {\n      id: 'zh-ko',\n      label: '中韩字幕 (中文+韩文)',\n      src: '/api/subtitles/default_zh-ko.vtt'\n    }\n  ]\n\n  const handleSubtitleChange = async (subtitleId: string) => {\n    const video = videoRef.current\n    if (!video) return\n\n    addDebugInfo(`切换字幕: ${subtitleId}`)\n\n    // 禁用所有现有字幕轨道\n    const tracks = video.textTracks\n    for (let i = 0; i < tracks.length; i++) {\n      tracks[i].mode = 'disabled'\n      addDebugInfo(`禁用轨道 ${i}: ${tracks[i].label}`)\n    }\n\n    if (subtitleId === 'none') {\n      setSelectedSubtitle('none')\n      return\n    }\n\n    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)\n    if (selectedTrack && selectedTrack.src) {\n      setSelectedSubtitle(subtitleId)\n      addDebugInfo(`选中轨道: ${selectedTrack.label}, 文件: ${selectedTrack.src}`)\n\n      // 清除现有的字幕轨道元素\n      const existingTracks = video.querySelectorAll('track')\n      existingTracks.forEach((track, index) => {\n        track.remove()\n        addDebugInfo(`移除现有轨道 ${index}`)\n      })\n\n      // 创建新的字幕轨道元素\n      const trackElement = document.createElement('track')\n      trackElement.kind = 'subtitles'\n      trackElement.src = selectedTrack.src\n      trackElement.srcLang = 'zh'\n      trackElement.label = selectedTrack.label\n      trackElement.default = true\n\n      addDebugInfo(`创建新轨道: ${trackElement.label}`)\n\n      // 添加事件监听器\n      trackElement.addEventListener('load', () => {\n        addDebugInfo(`轨道加载成功: ${trackElement.label}`)\n        addDebugInfo(`字幕条目数量: ${trackElement.track.cues?.length || 0}`)\n        \n        // 强制启用字幕\n        trackElement.track.mode = 'disabled'\n        setTimeout(() => {\n          trackElement.track.mode = 'showing'\n          addDebugInfo(`轨道模式设置为: ${trackElement.track.mode}`)\n          \n          // 检查当前时间的字幕\n          if (trackElement.track.cues && trackElement.track.cues.length > 0) {\n            const currentCue = Array.from(trackElement.track.cues).find(cue => \n              video.currentTime >= cue.startTime && video.currentTime <= cue.endTime\n            )\n            if (currentCue) {\n              addDebugInfo(`当前时间字幕: ${currentCue.text}`)\n            }\n          }\n        }, 100)\n      })\n\n      trackElement.addEventListener('error', (e) => {\n        addDebugInfo(`轨道加载失败: ${e}`)\n      })\n\n      // 添加到video元素\n      video.appendChild(trackElement)\n      addDebugInfo(`轨道已添加到视频元素`)\n    }\n  }\n\n  const handleTimeUpdate = () => {\n    const video = videoRef.current\n    if (video) {\n      setCurrentTime(video.currentTime)\n      \n      // 检查当前活跃的字幕\n      const tracks = video.textTracks\n      for (let i = 0; i < tracks.length; i++) {\n        const track = tracks[i]\n        if (track.mode === 'showing' && track.activeCues && track.activeCues.length > 0) {\n          const activeCue = track.activeCues[0]\n          // 只在字幕变化时记录，避免过多日志\n          if (activeCue && activeCue.text) {\n            // 可以在这里添加当前字幕的调试信息\n          }\n        }\n      }\n    }\n  }\n\n  const togglePlay = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    if (isPlaying) {\n      video.pause()\n    } else {\n      video.play()\n    }\n  }\n\n  useEffect(() => {\n    // 初始化中韩字幕\n    setTimeout(() => {\n      handleSubtitleChange('zh-ko')\n    }, 1000)\n  }, [])\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">字幕显示测试页面</h1>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* 视频播放器 */}\n          <div className=\"space-y-4\">\n            <div className=\"bg-black rounded-lg overflow-hidden relative\">\n              <video\n                ref={videoRef}\n                className=\"w-full h-auto\"\n                onPlay={() => setIsPlaying(true)}\n                onPause={() => setIsPlaying(false)}\n                onTimeUpdate={handleTimeUpdate}\n                crossOrigin=\"anonymous\"\n                controls\n              >\n                <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n                {/* 直接在HTML中添加字幕轨道 */}\n                <track\n                  kind=\"subtitles\"\n                  src=\"/api/subtitles/default_zh-ko.vtt\"\n                  srcLang=\"zh\"\n                  label=\"中韩字幕\"\n                  default\n                />\n                您的浏览器不支持视频播放。\n              </video>\n            </div>\n\n            {/* 控制按钮 */}\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={togglePlay}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                {isPlaying ? '暂停' : '播放'}\n              </button>\n              \n              <button\n                onClick={() => handleSubtitleChange('zh-ko')}\n                className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n              >\n                重新加载字幕\n              </button>\n            </div>\n\n            {/* 字幕选择 */}\n            <div className=\"bg-gray-800 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-2\">字幕选择</h3>\n              <div className=\"space-y-2\">\n                {subtitleTracks.map((track) => (\n                  <button\n                    key={track.id}\n                    onClick={() => handleSubtitleChange(track.id)}\n                    className={`w-full p-2 rounded text-left transition-colors ${\n                      selectedSubtitle === track.id\n                        ? 'bg-red-600 text-white'\n                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                    }`}\n                  >\n                    {track.label}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* 调试信息 */}\n          <div className=\"space-y-4\">\n            {/* 状态信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-2\">当前状态</h3>\n              <div className=\"text-gray-300 space-y-1 text-sm\">\n                <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>\n                <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>\n                <p>当前时间: {currentTime.toFixed(2)}s</p>\n                <p>视频文本轨道数量: {videoRef.current?.textTracks.length || 0}</p>\n                \n                {videoRef.current && Array.from(videoRef.current.textTracks).map((track, index) => (\n                  <div key={index} className=\"ml-2 text-xs\">\n                    <p>轨道 {index}: {track.label} (模式: {track.mode})</p>\n                    <p>字幕条目: {track.cues?.length || 0}</p>\n                    <p>活跃字幕: {track.activeCues?.length || 0}</p>\n                    {track.activeCues && track.activeCues.length > 0 && (\n                      <p className=\"text-yellow-400\">当前字幕: {track.activeCues[0].text}</p>\n                    )}\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* 调试日志 */}\n            <div className=\"bg-gray-800 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-2\">调试日志</h3>\n              <div className=\"bg-gray-900 rounded p-3 max-h-60 overflow-y-auto\">\n                {debugInfo.map((info, index) => (\n                  <div key={index} className=\"text-xs text-gray-300 mb-1\">\n                    {info}\n                  </div>\n                ))}\n              </div>\n              <button\n                onClick={() => setDebugInfo([])}\n                className=\"mt-2 px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600\"\n              >\n                清除日志\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-8 bg-gray-800 rounded-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-white mb-2\">测试说明</h3>\n          <div className=\"text-gray-300 text-sm space-y-2\">\n            <p>1. 点击播放按钮开始播放视频</p>\n            <p>2. 观察视频底部是否显示中韩双语字幕</p>\n            <p>3. 查看右侧的调试信息，确认字幕轨道状态</p>\n            <p>4. 如果字幕不显示，点击\"重新加载字幕\"按钮</p>\n            <p>5. 检查\"活跃字幕\"是否有内容显示</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe,CAAC;QACpB,aAAa,CAAA,OAAQ;mBAAI;gBAAM,GAAG,IAAI,OAAO,kBAAkB,GAAG,EAAE,EAAE,MAAM;aAAC;IAC/E;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;QACP;KACD;IAED,MAAM,uBAAuB,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,aAAa,CAAC,MAAM,EAAE,YAAY;QAElC,aAAa;QACb,MAAM,SAAS,MAAM,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;YACjB,aAAa,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE;QAC9C;QAEA,IAAI,eAAe,QAAQ;YACzB,oBAAoB;YACpB;QACF;QAEA,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChE,IAAI,iBAAiB,cAAc,GAAG,EAAE;YACtC,oBAAoB;YACpB,aAAa,CAAC,MAAM,EAAE,cAAc,KAAK,CAAC,MAAM,EAAE,cAAc,GAAG,EAAE;YAErE,cAAc;YACd,MAAM,iBAAiB,MAAM,gBAAgB,CAAC;YAC9C,eAAe,OAAO,CAAC,CAAC,OAAO;gBAC7B,MAAM,MAAM;gBACZ,aAAa,CAAC,OAAO,EAAE,OAAO;YAChC;YAEA,aAAa;YACb,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,IAAI,GAAG;YACpB,aAAa,GAAG,GAAG,cAAc,GAAG;YACpC,aAAa,OAAO,GAAG;YACvB,aAAa,KAAK,GAAG,cAAc,KAAK;YACxC,aAAa,OAAO,GAAG;YAEvB,aAAa,CAAC,OAAO,EAAE,aAAa,KAAK,EAAE;YAE3C,UAAU;YACV,aAAa,gBAAgB,CAAC,QAAQ;gBACpC,aAAa,CAAC,QAAQ,EAAE,aAAa,KAAK,EAAE;gBAC5C,aAAa,CAAC,QAAQ,EAAE,aAAa,KAAK,CAAC,IAAI,EAAE,UAAU,GAAG;gBAE9D,SAAS;gBACT,aAAa,KAAK,CAAC,IAAI,GAAG;gBAC1B,WAAW;oBACT,aAAa,KAAK,CAAC,IAAI,GAAG;oBAC1B,aAAa,CAAC,SAAS,EAAE,aAAa,KAAK,CAAC,IAAI,EAAE;oBAElD,YAAY;oBACZ,IAAI,aAAa,KAAK,CAAC,IAAI,IAAI,aAAa,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;wBACjE,MAAM,aAAa,MAAM,IAAI,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,MAC1D,MAAM,WAAW,IAAI,IAAI,SAAS,IAAI,MAAM,WAAW,IAAI,IAAI,OAAO;wBAExE,IAAI,YAAY;4BACd,aAAa,CAAC,QAAQ,EAAE,WAAW,IAAI,EAAE;wBAC3C;oBACF;gBACF,GAAG;YACL;YAEA,aAAa,gBAAgB,CAAC,SAAS,CAAC;gBACtC,aAAa,CAAC,QAAQ,EAAE,GAAG;YAC7B;YAEA,aAAa;YACb,MAAM,WAAW,CAAC;YAClB,aAAa,CAAC,UAAU,CAAC;QAC3B;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,OAAO;YACT,eAAe,MAAM,WAAW;YAEhC,YAAY;YACZ,MAAM,SAAS,MAAM,UAAU;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACtC,MAAM,QAAQ,MAAM,CAAC,EAAE;gBACvB,IAAI,MAAM,IAAI,KAAK,aAAa,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,GAAG;oBAC/E,MAAM,YAAY,MAAM,UAAU,CAAC,EAAE;oBACrC,mBAAmB;oBACnB,IAAI,aAAa,UAAU,IAAI,EAAE;oBAC/B,mBAAmB;oBACrB;gBACF;YACF;QACF;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,WAAW;YACb,MAAM,KAAK;QACb,OAAO;YACL,MAAM,IAAI;QACZ;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;QACV,WAAW;YACT,qBAAqB;QACvB,GAAG;IACL,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,KAAK;wCACL,WAAU;wCACV,QAAQ,IAAM,aAAa;wCAC3B,SAAS,IAAM,aAAa;wCAC5B,cAAc;wCACd,aAAY;wCACZ,QAAQ;;0DAER,8OAAC;gDAAO,KAAI;gDAA0C,MAAK;;;;;;0DAE3D,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,SAAQ;gDACR,OAAM;gDACN,OAAO;;;;;;4CACP;;;;;;;;;;;;8CAMN,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAET,YAAY,OAAO;;;;;;sDAGtB,8OAAC;4CACC,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDACX;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;oDAEC,SAAS,IAAM,qBAAqB,MAAM,EAAE;oDAC5C,WAAW,CAAC,+CAA+C,EACzD,qBAAqB,MAAM,EAAE,GACzB,0BACA,+CACJ;8DAED,MAAM,KAAK;mDARP,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;sCAgBvB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAE;wDAAO,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB;;;;;;;8DAC/D,8OAAC;;wDAAE;wDAAO,YAAY,QAAQ;;;;;;;8DAC9B,8OAAC;;wDAAE;wDAAO,YAAY,OAAO,CAAC;wDAAG;;;;;;;8DACjC,8OAAC;;wDAAE;wDAAW,SAAS,OAAO,EAAE,WAAW,UAAU;;;;;;;gDAEpD,SAAS,OAAO,IAAI,MAAM,IAAI,CAAC,SAAS,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,OAAO,sBACvE,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;;oEAAE;oEAAI;oEAAM;oEAAG,MAAM,KAAK;oEAAC;oEAAO,MAAM,IAAI;oEAAC;;;;;;;0EAC9C,8OAAC;;oEAAE;oEAAO,MAAM,IAAI,EAAE,UAAU;;;;;;;0EAChC,8OAAC;;oEAAE;oEAAO,MAAM,UAAU,EAAE,UAAU;;;;;;;4DACrC,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,mBAC7C,8OAAC;gEAAE,WAAU;;oEAAkB;oEAAO,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI;;;;;;;;uDALxD;;;;;;;;;;;;;;;;;8CAahB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;oDAAgB,WAAU;8DACxB;mDADO;;;;;;;;;;sDAKd,8OAAC;4CACC,SAAS,IAAM,aAAa,EAAE;4CAC9B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAOP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;8CACH,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}