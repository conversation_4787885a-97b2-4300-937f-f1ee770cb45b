{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/subtitle-test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\n\nexport default function SubtitleTestPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [selectedSubtitle, setSelectedSubtitle] = useState('zh')\n  const [isPlaying, setIsPlaying] = useState(false)\n\n  const subtitleTracks = [\n    {\n      id: 'none',\n      label: '无字幕',\n      language: 'none',\n      src: ''\n    },\n    {\n      id: 'zh',\n      label: '中文字幕',\n      language: 'zh',\n      src: '/subtitles/default_zh.vtt'\n    },\n    {\n      id: 'zh-ko',\n      label: '中韩字幕 (中文+韩文)',\n      language: 'zh-ko',\n      src: '/subtitles/default_zh-ko.vtt'\n    },\n    {\n      id: 'zh-en',\n      label: '中英字幕 (中文+英文)',\n      language: 'zh-en',\n      src: '/subtitles/default_zh-en.vtt'\n    }\n  ]\n\n  const handleSubtitleChange = (subtitleId: string) => {\n    const video = videoRef.current\n    if (!video) return\n\n    console.log('切换字幕:', subtitleId)\n\n    // 禁用所有现有字幕轨道\n    const tracks = video.textTracks\n    for (let i = 0; i < tracks.length; i++) {\n      tracks[i].mode = 'disabled'\n    }\n\n    if (subtitleId === 'none') {\n      setSelectedSubtitle('none')\n      return\n    }\n\n    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)\n    if (selectedTrack && selectedTrack.src) {\n      setSelectedSubtitle(subtitleId)\n\n      // 清除现有的字幕轨道元素\n      const existingTracks = video.querySelectorAll('track')\n      existingTracks.forEach(track => track.remove())\n\n      // 创建新的字幕轨道元素\n      const trackElement = document.createElement('track')\n      trackElement.kind = 'subtitles'\n      trackElement.src = selectedTrack.src\n      trackElement.srcLang = selectedTrack.language.split('-')[0]\n      trackElement.label = selectedTrack.label\n      trackElement.default = true\n\n      // 添加到video元素\n      video.appendChild(trackElement)\n\n      // 启用字幕显示\n      setTimeout(() => {\n        const textTracks = video.textTracks\n        for (let i = 0; i < textTracks.length; i++) {\n          const track = textTracks[i]\n          if (track.label === selectedTrack.label) {\n            track.mode = 'showing'\n            console.log('成功启用字幕轨道:', track.label, '文件路径:', selectedTrack.src)\n            break\n          }\n        }\n      }, 200)\n    }\n  }\n\n  const togglePlay = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    if (isPlaying) {\n      video.pause()\n    } else {\n      video.play()\n    }\n  }\n\n  useEffect(() => {\n    // 初始化默认字幕\n    setTimeout(() => {\n      handleSubtitleChange('zh')\n    }, 1000)\n  }, [])\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">字幕功能测试页面</h1>\n        \n        <div className=\"bg-black rounded-lg overflow-hidden mb-6\">\n          <video\n            ref={videoRef}\n            className=\"w-full h-auto\"\n            onPlay={() => setIsPlaying(true)}\n            onPause={() => setIsPlaying(false)}\n            crossOrigin=\"anonymous\"\n            controls\n            style={{\n              '--subtitle-font-size': '18px',\n              '--subtitle-line-height': '1.4',\n              '--subtitle-background': 'rgba(0, 0, 0, 0.8)',\n              '--subtitle-color': 'white'\n            } as React.CSSProperties}\n          >\n            <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n            您的浏览器不支持视频播放。\n          </video>\n        </div>\n\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-white mb-4\">字幕选择</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n            {subtitleTracks.map((track) => (\n              <button\n                key={track.id}\n                onClick={() => handleSubtitleChange(track.id)}\n                className={`p-3 rounded-lg text-sm font-medium transition-colors ${\n                  selectedSubtitle === track.id\n                    ? 'bg-red-600 text-white'\n                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                }`}\n              >\n                {track.label}\n              </button>\n            ))}\n          </div>\n\n          <div className=\"mt-6\">\n            <h3 className=\"text-lg font-semibold text-white mb-2\">当前状态</h3>\n            <div className=\"text-gray-300 space-y-1\">\n              <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>\n              <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>\n            </div>\n          </div>\n\n          <div className=\"mt-6\">\n            <h3 className=\"text-lg font-semibold text-white mb-2\">测试说明</h3>\n            <div className=\"text-gray-300 text-sm space-y-2\">\n              <p>1. 点击不同的字幕选项来测试字幕切换功能</p>\n              <p>2. 中韩字幕应该显示中文和韩文两行</p>\n              <p>3. 中英字幕应该显示中文和英文两行</p>\n              <p>4. 检查浏览器控制台的日志信息</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;KACD;IAED,MAAM,uBAAuB,CAAC;QAC5B,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,QAAQ,GAAG,CAAC,SAAS;QAErB,aAAa;QACb,MAAM,SAAS,MAAM,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;QACnB;QAEA,IAAI,eAAe,QAAQ;YACzB,oBAAoB;YACpB;QACF;QAEA,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChE,IAAI,iBAAiB,cAAc,GAAG,EAAE;YACtC,oBAAoB;YAEpB,cAAc;YACd,MAAM,iBAAiB,MAAM,gBAAgB,CAAC;YAC9C,eAAe,OAAO,CAAC,CAAA,QAAS,MAAM,MAAM;YAE5C,aAAa;YACb,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,IAAI,GAAG;YACpB,aAAa,GAAG,GAAG,cAAc,GAAG;YACpC,aAAa,OAAO,GAAG,cAAc,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3D,aAAa,KAAK,GAAG,cAAc,KAAK;YACxC,aAAa,OAAO,GAAG;YAEvB,aAAa;YACb,MAAM,WAAW,CAAC;YAElB,SAAS;YACT,WAAW;gBACT,MAAM,aAAa,MAAM,UAAU;gBACnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;oBAC1C,MAAM,QAAQ,UAAU,CAAC,EAAE;oBAC3B,IAAI,MAAM,KAAK,KAAK,cAAc,KAAK,EAAE;wBACvC,MAAM,IAAI,GAAG;wBACb,QAAQ,GAAG,CAAC,aAAa,MAAM,KAAK,EAAE,SAAS,cAAc,GAAG;wBAChE;oBACF;gBACF;YACF,GAAG;QACL;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,WAAW;YACb,MAAM,KAAK;QACb,OAAO;YACL,MAAM,IAAI;QACZ;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UAAU;QACV,WAAW;YACT,qBAAqB;QACvB,GAAG;IACL,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,QAAQ,IAAM,aAAa;wBAC3B,SAAS,IAAM,aAAa;wBAC5B,aAAY;wBACZ,QAAQ;wBACR,OAAO;4BACL,wBAAwB;4BACxB,0BAA0B;4BAC1B,yBAAyB;4BACzB,oBAAoB;wBACtB;;0CAEA,8OAAC;gCAAO,KAAI;gCAA0C,MAAK;;;;;;4BAAc;;;;;;;;;;;;8BAK7E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;oCAEC,SAAS,IAAM,qBAAqB,MAAM,EAAE;oCAC5C,WAAW,CAAC,qDAAqD,EAC/D,qBAAqB,MAAM,EAAE,GACzB,0BACA,+CACJ;8CAED,MAAM,KAAK;mCARP,MAAM,EAAE;;;;;;;;;;sCAanB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDAAO,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,mBAAmB;;;;;;;sDAC/D,8OAAC;;gDAAE;gDAAO,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}]}