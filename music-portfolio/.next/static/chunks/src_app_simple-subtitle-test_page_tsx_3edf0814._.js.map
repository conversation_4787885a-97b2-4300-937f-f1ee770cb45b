{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/simple-subtitle-test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useRef, useEffect, useState } from 'react'\n\nexport default function SimpleSubtitleTestPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [trackInfo, setTrackInfo] = useState<any[]>([])\n\n  useEffect(() => {\n    const video = videoRef.current\n    if (!video) return\n\n    const updateTrackInfo = () => {\n      const tracks = Array.from(video.textTracks)\n      const info = tracks.map((track, index) => ({\n        index,\n        label: track.label,\n        language: track.language,\n        mode: track.mode,\n        cues: track.cues?.length || 0,\n        activeCues: track.activeCues?.length || 0,\n        currentCue: track.activeCues && track.activeCues.length > 0 ? track.activeCues[0].text : null\n      }))\n      setTrackInfo(info)\n    }\n\n    // 监听字幕轨道变化\n    video.addEventListener('loadedmetadata', updateTrackInfo)\n    video.addEventListener('timeupdate', updateTrackInfo)\n\n    // 手动启用字幕\n    const enableSubtitles = () => {\n      const tracks = video.textTracks\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'showing'\n        console.log(`启用轨道 ${i}:`, tracks[i].label)\n      }\n      updateTrackInfo()\n    }\n\n    // 延迟启用字幕\n    setTimeout(enableSubtitles, 1000)\n\n    return () => {\n      video.removeEventListener('loadedmetadata', updateTrackInfo)\n      video.removeEventListener('timeupdate', updateTrackInfo)\n    }\n  }, [])\n\n  const forceEnableSubtitles = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    const tracks = video.textTracks\n    for (let i = 0; i < tracks.length; i++) {\n      tracks[i].mode = 'showing'\n      console.log(`强制启用轨道 ${i}:`, tracks[i].label, tracks[i].mode)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">简单字幕测试</h1>\n        \n        <div className=\"space-y-6\">\n          {/* 视频播放器 */}\n          <div className=\"bg-black rounded-lg overflow-hidden\">\n            <video\n              ref={videoRef}\n              className=\"w-full h-auto\"\n              controls\n              crossOrigin=\"anonymous\"\n              style={{\n                // 确保字幕样式\n                '--webkit-media-text-track-display': 'block'\n              } as React.CSSProperties}\n            >\n              <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n              \n              {/* 测试字幕轨道 */}\n              <track\n                kind=\"subtitles\"\n                src=\"/api/subtitles/test_simple.vtt\"\n                srcLang=\"zh\"\n                label=\"简单测试字幕\"\n                default\n              />\n              <track\n                kind=\"subtitles\"\n                src=\"/api/subtitles/default_zh-ko.vtt\"\n                srcLang=\"zh\"\n                label=\"中韩字幕\"\n              />\n              \n              您的浏览器不支持视频播放。\n            </video>\n          </div>\n\n          {/* 控制按钮 */}\n          <div className=\"flex space-x-4\">\n            <button\n              onClick={forceEnableSubtitles}\n              className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n            >\n              强制启用字幕\n            </button>\n          </div>\n\n          {/* 字幕轨道信息 */}\n          <div className=\"bg-gray-800 rounded-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-white mb-4\">字幕轨道信息</h2>\n            {trackInfo.length === 0 ? (\n              <p className=\"text-gray-400\">没有检测到字幕轨道</p>\n            ) : (\n              <div className=\"space-y-4\">\n                {trackInfo.map((track) => (\n                  <div key={track.index} className=\"bg-gray-700 rounded p-4\">\n                    <h3 className=\"text-lg font-medium text-white mb-2\">\n                      轨道 {track.index}: {track.label}\n                    </h3>\n                    <div className=\"text-sm text-gray-300 space-y-1\">\n                      <p>语言: {track.language}</p>\n                      <p>模式: <span className={track.mode === 'showing' ? 'text-green-400' : 'text-red-400'}>{track.mode}</span></p>\n                      <p>字幕条目数: {track.cues}</p>\n                      <p>活跃字幕数: {track.activeCues}</p>\n                      {track.currentCue && (\n                        <div className=\"mt-2 p-2 bg-gray-600 rounded\">\n                          <p className=\"text-yellow-400 font-medium\">当前字幕:</p>\n                          <p className=\"text-white whitespace-pre-line\">{track.currentCue}</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* 说明 */}\n          <div className=\"bg-gray-800 rounded-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-white mb-4\">测试说明</h2>\n            <div className=\"text-gray-300 text-sm space-y-2\">\n              <p>1. 这个页面使用HTML原生的 &lt;track&gt; 元素来加载字幕</p>\n              <p>2. 视频加载后会自动尝试启用字幕</p>\n              <p>3. 如果字幕没有显示，点击\"强制启用字幕\"按钮</p>\n              <p>4. 观察下方的字幕轨道信息，确认字幕是否正确加载</p>\n              <p>5. 如果\"当前字幕\"有内容但视频上没有显示，可能是浏览器渲染问题</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,OAAO;YAEZ,MAAM;oEAAkB;oBACtB,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,UAAU;oBAC1C,MAAM,OAAO,OAAO,GAAG;iFAAC,CAAC,OAAO;gCAKxB,aACM;mCAN6B;gCACzC;gCACA,OAAO,MAAM,KAAK;gCAClB,UAAU,MAAM,QAAQ;gCACxB,MAAM,MAAM,IAAI;gCAChB,MAAM,EAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,MAAM,KAAI;gCAC5B,YAAY,EAAA,oBAAA,MAAM,UAAU,cAAhB,wCAAA,kBAAkB,MAAM,KAAI;gCACxC,YAAY,MAAM,UAAU,IAAI,MAAM,UAAU,CAAC,MAAM,GAAG,IAAI,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG;4BAC3F;;;oBACA,aAAa;gBACf;;YAEA,WAAW;YACX,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YAErC,SAAS;YACT,MAAM;oEAAkB;oBACtB,MAAM,SAAS,MAAM,UAAU;oBAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;wBACjB,QAAQ,GAAG,CAAC,AAAC,QAAS,OAAF,GAAE,MAAI,MAAM,CAAC,EAAE,CAAC,KAAK;oBAC3C;oBACA;gBACF;;YAEA,SAAS;YACT,WAAW,iBAAiB;YAE5B;oDAAO;oBACL,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,cAAc;gBAC1C;;QACF;2CAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,SAAS,MAAM,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;YACjB,QAAQ,GAAG,CAAC,AAAC,UAAW,OAAF,GAAE,MAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI;QAC7D;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,QAAQ;gCACR,aAAY;gCACZ,OAAO;oCACL,SAAS;oCACT,qCAAqC;gCACvC;;kDAEA,6LAAC;wCAAO,KAAI;wCAA0C,MAAK;;;;;;kDAG3D,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,SAAQ;wCACR,OAAM;wCACN,OAAO;;;;;;kDAET,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,SAAQ;wCACR,OAAM;;;;;;oCACN;;;;;;;;;;;;sCAON,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;gCACrD,UAAU,MAAM,KAAK,kBACpB,6LAAC;oCAAE,WAAU;8CAAgB;;;;;yDAE7B,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,sBACd,6LAAC;4CAAsB,WAAU;;8DAC/B,6LAAC;oDAAG,WAAU;;wDAAsC;wDAC9C,MAAM,KAAK;wDAAC;wDAAG,MAAM,KAAK;;;;;;;8DAEhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAE;gEAAK,MAAM,QAAQ;;;;;;;sEACtB,6LAAC;;gEAAE;8EAAI,6LAAC;oEAAK,WAAW,MAAM,IAAI,KAAK,YAAY,mBAAmB;8EAAiB,MAAM,IAAI;;;;;;;;;;;;sEACjG,6LAAC;;gEAAE;gEAAQ,MAAM,IAAI;;;;;;;sEACrB,6LAAC;;gEAAE;gEAAQ,MAAM,UAAU;;;;;;;wDAC1B,MAAM,UAAU,kBACf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAA8B;;;;;;8EAC3C,6LAAC;oEAAE,WAAU;8EAAkC,MAAM,UAAU;;;;;;;;;;;;;;;;;;;2CAZ7D,MAAM,KAAK;;;;;;;;;;;;;;;;sCAuB7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GAtJwB;KAAA", "debugId": null}}]}