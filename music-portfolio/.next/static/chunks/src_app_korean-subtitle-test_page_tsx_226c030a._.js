(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/korean-subtitle-test/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>KoreanSubtitleTestPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function KoreanSubtitleTestPage() {
    var _subtitleTracks_find, _videoRef_current;
    _s();
    const videoRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [selectedSubtitle, setSelectedSubtitle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('korean');
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [debugInfo, setDebugInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const addDebugInfo = (info)=>{
        setDebugInfo((prev)=>[
                ...prev,
                "".concat(new Date().toLocaleTimeString(), ": ").concat(info)
            ]);
    };
    const subtitleTracks = [
        {
            id: 'none',
            label: '无字幕',
            language: 'none',
            src: ''
        },
        {
            id: 'korean',
            label: '한국어 (韩文)',
            language: 'ko',
            src: '/api/subtitles/movie_1755665391178_ko.vtt'
        },
        {
            id: 'zh-ko',
            label: '中韩字幕 (中文+韩文)',
            language: 'zh-ko',
            src: '/api/subtitles/default_zh-ko.vtt'
        }
    ];
    const handleSubtitleChange = async (subtitleId)=>{
        const video = videoRef.current;
        if (!video) return;
        addDebugInfo("切换字幕: ".concat(subtitleId));
        // 禁用所有现有字幕轨道
        const tracks = video.textTracks;
        for(let i = 0; i < tracks.length; i++){
            tracks[i].mode = 'disabled';
            addDebugInfo("禁用轨道 ".concat(i, ": ").concat(tracks[i].label));
        }
        if (subtitleId === 'none') {
            setSelectedSubtitle('none');
            return;
        }
        const selectedTrack = subtitleTracks.find((track)=>track.id === subtitleId);
        if (selectedTrack && selectedTrack.src) {
            setSelectedSubtitle(subtitleId);
            addDebugInfo("选中轨道: ".concat(selectedTrack.label, ", 文件: ").concat(selectedTrack.src));
            // 检查字幕文件是否存在
            try {
                const response = await fetch(selectedTrack.src, {
                    method: 'HEAD'
                });
                addDebugInfo("字幕文件检查: ".concat(response.ok ? '存在' : '不存在', " (").concat(response.status, ")"));
            } catch (error) {
                addDebugInfo("字幕文件检查失败: ".concat(error));
            }
            // 清除现有的字幕轨道元素
            const existingTracks = video.querySelectorAll('track');
            existingTracks.forEach((track, index)=>{
                track.remove();
                addDebugInfo("移除现有轨道 ".concat(index));
            });
            // 创建新的字幕轨道元素
            const trackElement = document.createElement('track');
            trackElement.kind = 'subtitles';
            trackElement.src = selectedTrack.src;
            trackElement.srcLang = selectedTrack.language.split('-')[0];
            trackElement.label = selectedTrack.label;
            trackElement.default = true;
            addDebugInfo("创建新轨道: ".concat(trackElement.label, ", 语言: ").concat(trackElement.srcLang));
            // 添加事件监听器
            trackElement.addEventListener('load', ()=>{
                addDebugInfo("轨道加载成功: ".concat(trackElement.label));
                trackElement.track.mode = 'showing';
                addDebugInfo("轨道模式设置为: showing");
            });
            trackElement.addEventListener('error', (e)=>{
                addDebugInfo("轨道加载失败: ".concat(e));
            });
            // 添加到video元素
            video.appendChild(trackElement);
            addDebugInfo("轨道已添加到视频元素");
            // 延迟启用字幕显示
            setTimeout(()=>{
                const textTracks = video.textTracks;
                addDebugInfo("当前文本轨道数量: ".concat(textTracks.length));
                for(let i = 0; i < textTracks.length; i++){
                    const track = textTracks[i];
                    addDebugInfo("轨道 ".concat(i, ": ").concat(track.label, ", 模式: ").concat(track.mode, ", 语言: ").concat(track.language));
                    if (track.label === selectedTrack.label) {
                        track.mode = 'showing';
                        addDebugInfo("成功启用轨道: ".concat(track.label));
                        break;
                    }
                }
            }, 200);
        }
    };
    const togglePlay = ()=>{
        const video = videoRef.current;
        if (!video) return;
        if (isPlaying) {
            video.pause();
        } else {
            video.play();
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "KoreanSubtitleTestPage.useEffect": ()=>{
            // 初始化韩语字幕
            setTimeout({
                "KoreanSubtitleTestPage.useEffect": ()=>{
                    handleSubtitleChange('korean');
                }
            }["KoreanSubtitleTestPage.useEffect"], 1000);
        }
    }["KoreanSubtitleTestPage.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-900 p-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-6xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-3xl font-bold text-white mb-8",
                    children: "韩语字幕测试页面"
                }, void 0, false, {
                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                    lineNumber: 139,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-2 gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-black rounded-lg overflow-hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                ref: videoRef,
                                className: "w-full h-auto",
                                onPlay: ()=>setIsPlaying(true),
                                onPause: ()=>setIsPlaying(false),
                                crossOrigin: "anonymous",
                                controls: true,
                                style: {
                                    '--subtitle-font-size': '18px',
                                    '--subtitle-line-height': '1.4',
                                    '--subtitle-background': 'rgba(0, 0, 0, 0.8)',
                                    '--subtitle-color': 'white'
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("source", {
                                        src: "/uploads/movies/movie_1755665391178.mp4",
                                        type: "video/mp4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                        lineNumber: 158,
                                        columnNumber: 15
                                    }, this),
                                    "您的浏览器不支持视频播放。"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                lineNumber: 144,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                            lineNumber: 143,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-white mb-4",
                                            children: "字幕选择"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                            lineNumber: 167,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-2",
                                            children: subtitleTracks.map((track)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    onClick: ()=>handleSubtitleChange(track.id),
                                                    className: "w-full p-3 rounded-lg text-left font-medium transition-colors ".concat(selectedSubtitle === track.id ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'),
                                                    children: [
                                                        track.label,
                                                        track.src && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs text-gray-400 mt-1",
                                                            children: [
                                                                "文件: ",
                                                                track.src
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                                            lineNumber: 181,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, track.id, true, {
                                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                            lineNumber: 168,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 166,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-semibold text-white mb-2",
                                            children: "当前状态"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                            lineNumber: 192,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-gray-300 space-y-1 text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "选中字幕: ",
                                                        (_subtitleTracks_find = subtitleTracks.find((t)=>t.id === selectedSubtitle)) === null || _subtitleTracks_find === void 0 ? void 0 : _subtitleTracks_find.label
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                                    lineNumber: 194,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "播放状态: ",
                                                        isPlaying ? '播放中' : '暂停'
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                                    lineNumber: 195,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "视频文本轨道数量: ",
                                                        ((_videoRef_current = videoRef.current) === null || _videoRef_current === void 0 ? void 0 : _videoRef_current.textTracks.length) || 0
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                                    lineNumber: 196,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                            lineNumber: 193,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 191,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-semibold text-white mb-2",
                                            children: "调试信息"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                            lineNumber: 202,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-gray-900 rounded p-3 max-h-60 overflow-y-auto",
                                            children: debugInfo.map((info, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-xs text-gray-300 mb-1",
                                                    children: info
                                                }, index, false, {
                                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                                    lineNumber: 205,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                            lineNumber: 203,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setDebugInfo([]),
                                            className: "mt-2 px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600",
                                            children: "清除日志"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                            lineNumber: 210,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 201,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                            lineNumber: 164,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-8 bg-gray-800 rounded-lg p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-semibold text-white mb-2",
                            children: "测试说明"
                        }, void 0, false, {
                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-gray-300 text-sm space-y-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: '1. 点击"한국어 (韩文)"按钮测试韩语字幕'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 223,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: '2. 点击"中韩字幕"按钮测试双语字幕'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 224,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: "3. 观察调试信息中的字幕加载过程"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 225,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: "4. 检查视频是否显示对应的字幕"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 226,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: "5. 如果字幕不显示，查看浏览器控制台的错误信息"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                                    lineNumber: 227,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                            lineNumber: 222,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
                    lineNumber: 220,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
            lineNumber: 138,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/korean-subtitle-test/page.tsx",
        lineNumber: 137,
        columnNumber: 5
    }, this);
}
_s(KoreanSubtitleTestPage, "KsbanlHXwx5Na1FKyOZ7QsbarxA=");
_c = KoreanSubtitleTestPage;
var _c;
__turbopack_context__.k.register(_c, "KoreanSubtitleTestPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_app_korean-subtitle-test_page_tsx_226c030a._.js.map