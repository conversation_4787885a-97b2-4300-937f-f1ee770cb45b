{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/korean-subtitle-test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\n\nexport default function KoreanSubtitleTestPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [selectedSubtitle, setSelectedSubtitle] = useState('korean')\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [debugInfo, setDebugInfo] = useState<string[]>([])\n\n  const addDebugInfo = (info: string) => {\n    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${info}`])\n  }\n\n  const subtitleTracks = [\n    {\n      id: 'none',\n      label: '无字幕',\n      language: 'none',\n      src: ''\n    },\n    {\n      id: 'korean',\n      label: '한국어 (韩文)',\n      language: 'ko',\n      src: '/api/subtitles/movie_1755665391178_ko.vtt'\n    },\n    {\n      id: 'zh-ko',\n      label: '中韩字幕 (中文+韩文)',\n      language: 'zh-ko',\n      src: '/api/subtitles/default_zh-ko.vtt'\n    }\n  ]\n\n  const handleSubtitleChange = async (subtitleId: string) => {\n    const video = videoRef.current\n    if (!video) return\n\n    addDebugInfo(`切换字幕: ${subtitleId}`)\n\n    // 禁用所有现有字幕轨道\n    const tracks = video.textTracks\n    for (let i = 0; i < tracks.length; i++) {\n      tracks[i].mode = 'disabled'\n      addDebugInfo(`禁用轨道 ${i}: ${tracks[i].label}`)\n    }\n\n    if (subtitleId === 'none') {\n      setSelectedSubtitle('none')\n      return\n    }\n\n    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)\n    if (selectedTrack && selectedTrack.src) {\n      setSelectedSubtitle(subtitleId)\n      addDebugInfo(`选中轨道: ${selectedTrack.label}, 文件: ${selectedTrack.src}`)\n\n      // 检查字幕文件是否存在\n      try {\n        const response = await fetch(selectedTrack.src, { method: 'HEAD' })\n        addDebugInfo(`字幕文件检查: ${response.ok ? '存在' : '不存在'} (${response.status})`)\n      } catch (error) {\n        addDebugInfo(`字幕文件检查失败: ${error}`)\n      }\n\n      // 清除现有的字幕轨道元素\n      const existingTracks = video.querySelectorAll('track')\n      existingTracks.forEach((track, index) => {\n        track.remove()\n        addDebugInfo(`移除现有轨道 ${index}`)\n      })\n\n      // 创建新的字幕轨道元素\n      const trackElement = document.createElement('track')\n      trackElement.kind = 'subtitles'\n      trackElement.src = selectedTrack.src\n      trackElement.srcLang = selectedTrack.language.split('-')[0]\n      trackElement.label = selectedTrack.label\n      trackElement.default = true\n\n      addDebugInfo(`创建新轨道: ${trackElement.label}, 语言: ${trackElement.srcLang}`)\n\n      // 添加事件监听器\n      trackElement.addEventListener('load', () => {\n        addDebugInfo(`轨道加载成功: ${trackElement.label}`)\n        trackElement.track.mode = 'showing'\n        addDebugInfo(`轨道模式设置为: showing`)\n      })\n\n      trackElement.addEventListener('error', (e) => {\n        addDebugInfo(`轨道加载失败: ${e}`)\n      })\n\n      // 添加到video元素\n      video.appendChild(trackElement)\n      addDebugInfo(`轨道已添加到视频元素`)\n\n      // 延迟启用字幕显示\n      setTimeout(() => {\n        const textTracks = video.textTracks\n        addDebugInfo(`当前文本轨道数量: ${textTracks.length}`)\n        \n        for (let i = 0; i < textTracks.length; i++) {\n          const track = textTracks[i]\n          addDebugInfo(`轨道 ${i}: ${track.label}, 模式: ${track.mode}, 语言: ${track.language}`)\n          \n          if (track.label === selectedTrack.label) {\n            track.mode = 'showing'\n            addDebugInfo(`成功启用轨道: ${track.label}`)\n            break\n          }\n        }\n      }, 200)\n    }\n  }\n\n  const togglePlay = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    if (isPlaying) {\n      video.pause()\n    } else {\n      video.play()\n    }\n  }\n\n  useEffect(() => {\n    // 初始化韩语字幕\n    setTimeout(() => {\n      handleSubtitleChange('korean')\n    }, 1000)\n  }, [])\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">韩语字幕测试页面</h1>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* 视频播放器 */}\n          <div className=\"bg-black rounded-lg overflow-hidden\">\n            <video\n              ref={videoRef}\n              className=\"w-full h-auto\"\n              onPlay={() => setIsPlaying(true)}\n              onPause={() => setIsPlaying(false)}\n              crossOrigin=\"anonymous\"\n              controls\n              style={{\n                '--subtitle-font-size': '18px',\n                '--subtitle-line-height': '1.4',\n                '--subtitle-background': 'rgba(0, 0, 0, 0.8)',\n                '--subtitle-color': 'white'\n              } as React.CSSProperties}\n            >\n              <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n              您的浏览器不支持视频播放。\n            </video>\n          </div>\n\n          {/* 控制面板 */}\n          <div className=\"space-y-6\">\n            {/* 字幕选择 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">字幕选择</h2>\n              <div className=\"space-y-2\">\n                {subtitleTracks.map((track) => (\n                  <button\n                    key={track.id}\n                    onClick={() => handleSubtitleChange(track.id)}\n                    className={`w-full p-3 rounded-lg text-left font-medium transition-colors ${\n                      selectedSubtitle === track.id\n                        ? 'bg-red-600 text-white'\n                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                    }`}\n                  >\n                    {track.label}\n                    {track.src && (\n                      <div className=\"text-xs text-gray-400 mt-1\">\n                        文件: {track.src}\n                      </div>\n                    )}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* 状态信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-white mb-2\">当前状态</h3>\n              <div className=\"text-gray-300 space-y-1 text-sm\">\n                <p>选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>\n                <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>\n                <p>视频文本轨道数量: {videoRef.current?.textTracks.length || 0}</p>\n              </div>\n            </div>\n\n            {/* 调试信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-white mb-2\">调试信息</h3>\n              <div className=\"bg-gray-900 rounded p-3 max-h-60 overflow-y-auto\">\n                {debugInfo.map((info, index) => (\n                  <div key={index} className=\"text-xs text-gray-300 mb-1\">\n                    {info}\n                  </div>\n                ))}\n              </div>\n              <button\n                onClick={() => setDebugInfo([])}\n                className=\"mt-2 px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600\"\n              >\n                清除日志\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-8 bg-gray-800 rounded-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-white mb-2\">测试说明</h3>\n          <div className=\"text-gray-300 text-sm space-y-2\">\n            <p>1. 点击\"한국어 (韩文)\"按钮测试韩语字幕</p>\n            <p>2. 点击\"中韩字幕\"按钮测试双语字幕</p>\n            <p>3. 观察调试信息中的字幕加载过程</p>\n            <p>4. 检查视频是否显示对应的字幕</p>\n            <p>5. 如果字幕不显示，查看浏览器控制台的错误信息</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;QA6LE,sBAEI;;IA9L5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,MAAM,eAAe,CAAC;QACpB,aAAa,CAAA,OAAQ;mBAAI;gBAAO,GAAsC,OAApC,IAAI,OAAO,kBAAkB,IAAG,MAAS,OAAL;aAAO;IAC/E;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,UAAU;YACV,KAAK;QACP;KACD;IAED,MAAM,uBAAuB,OAAO;QAClC,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,aAAa,AAAC,SAAmB,OAAX;QAEtB,aAAa;QACb,MAAM,SAAS,MAAM,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;YACjB,aAAa,AAAC,QAAa,OAAN,GAAE,MAAoB,OAAhB,MAAM,CAAC,EAAE,CAAC,KAAK;QAC5C;QAEA,IAAI,eAAe,QAAQ;YACzB,oBAAoB;YACpB;QACF;QAEA,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChE,IAAI,iBAAiB,cAAc,GAAG,EAAE;YACtC,oBAAoB;YACpB,aAAa,AAAC,SAAoC,OAA5B,cAAc,KAAK,EAAC,UAA0B,OAAlB,cAAc,GAAG;YAEnE,aAAa;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,cAAc,GAAG,EAAE;oBAAE,QAAQ;gBAAO;gBACjE,aAAa,AAAC,WAAyC,OAA/B,SAAS,EAAE,GAAG,OAAO,OAAM,MAAoB,OAAhB,SAAS,MAAM,EAAC;YACzE,EAAE,OAAO,OAAO;gBACd,aAAa,AAAC,aAAkB,OAAN;YAC5B;YAEA,cAAc;YACd,MAAM,iBAAiB,MAAM,gBAAgB,CAAC;YAC9C,eAAe,OAAO,CAAC,CAAC,OAAO;gBAC7B,MAAM,MAAM;gBACZ,aAAa,AAAC,UAAe,OAAN;YACzB;YAEA,aAAa;YACb,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,IAAI,GAAG;YACpB,aAAa,GAAG,GAAG,cAAc,GAAG;YACpC,aAAa,OAAO,GAAG,cAAc,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3D,aAAa,KAAK,GAAG,cAAc,KAAK;YACxC,aAAa,OAAO,GAAG;YAEvB,aAAa,AAAC,UAAoC,OAA3B,aAAa,KAAK,EAAC,UAA6B,OAArB,aAAa,OAAO;YAEtE,UAAU;YACV,aAAa,gBAAgB,CAAC,QAAQ;gBACpC,aAAa,AAAC,WAA6B,OAAnB,aAAa,KAAK;gBAC1C,aAAa,KAAK,CAAC,IAAI,GAAG;gBAC1B,aAAc;YAChB;YAEA,aAAa,gBAAgB,CAAC,SAAS,CAAC;gBACtC,aAAa,AAAC,WAAY,OAAF;YAC1B;YAEA,aAAa;YACb,MAAM,WAAW,CAAC;YAClB,aAAc;YAEd,WAAW;YACX,WAAW;gBACT,MAAM,aAAa,MAAM,UAAU;gBACnC,aAAa,AAAC,aAA8B,OAAlB,WAAW,MAAM;gBAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;oBAC1C,MAAM,QAAQ,UAAU,CAAC,EAAE;oBAC3B,aAAa,AAAC,MAAW,OAAN,GAAE,MAAwB,OAApB,MAAM,KAAK,EAAC,UAA2B,OAAnB,MAAM,IAAI,EAAC,UAAuB,OAAf,MAAM,QAAQ;oBAE9E,IAAI,MAAM,KAAK,KAAK,cAAc,KAAK,EAAE;wBACvC,MAAM,IAAI,GAAG;wBACb,aAAa,AAAC,WAAsB,OAAZ,MAAM,KAAK;wBACnC;oBACF;gBACF;YACF,GAAG;QACL;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,WAAW;YACb,MAAM,KAAK;QACb,OAAO;YACL,MAAM,IAAI;QACZ;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,UAAU;YACV;oDAAW;oBACT,qBAAqB;gBACvB;mDAAG;QACL;2CAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,QAAQ,IAAM,aAAa;gCAC3B,SAAS,IAAM,aAAa;gCAC5B,aAAY;gCACZ,QAAQ;gCACR,OAAO;oCACL,wBAAwB;oCACxB,0BAA0B;oCAC1B,yBAAyB;oCACzB,oBAAoB;gCACtB;;kDAEA,6LAAC;wCAAO,KAAI;wCAA0C,MAAK;;;;;;oCAAc;;;;;;;;;;;;sCAM7E,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;oDAEC,SAAS,IAAM,qBAAqB,MAAM,EAAE;oDAC5C,WAAW,AAAC,iEAIX,OAHC,qBAAqB,MAAM,EAAE,GACzB,0BACA;;wDAGL,MAAM,KAAK;wDACX,MAAM,GAAG,kBACR,6LAAC;4DAAI,WAAU;;gEAA6B;gEACrC,MAAM,GAAG;;;;;;;;mDAXb,MAAM,EAAE;;;;;;;;;;;;;;;;8CAoBrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;yDAAO,uBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAAlC,2CAAA,qBAAqD,KAAK;;;;;;;8DACpE,6LAAC;;wDAAE;wDAAO,YAAY,QAAQ;;;;;;;8DAC9B,6LAAC;;wDAAE;wDAAW,EAAA,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,UAAU,CAAC,MAAM,KAAI;;;;;;;;;;;;;;;;;;;8CAKzD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;oDAAgB,WAAU;8DACxB;mDADO;;;;;;;;;;sDAKd,6LAAC;4CACC,SAAS,IAAM,aAAa,EAAE;4CAC9B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAOP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;8CACH,6LAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GApOwB;KAAA", "debugId": null}}]}