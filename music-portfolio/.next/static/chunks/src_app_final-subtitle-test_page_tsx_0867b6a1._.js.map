{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/final-subtitle-test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\n\nexport default function FinalSubtitleTestPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [selectedSubtitle, setSelectedSubtitle] = useState('zh-ko')\n  const [isPlaying, setIsPlaying] = useState(false)\n  const [showCustomSubtitle, setShowCustomSubtitle] = useState(false)\n  const [currentSubtitleText, setCurrentSubtitleText] = useState('')\n\n  // 字幕数据\n  const subtitleData = {\n    'zh-ko': [\n      { start: 0, end: 5, text: '欢迎观看这部精彩的电影\\n이 멋진 영화를 시청해 주셔서 환영합니다' },\n      { start: 5, end: 10, text: '这是中韩双语字幕演示\\n이것은 중국어-한국어 이중 자막 데모입니다' },\n      { start: 10, end: 15, text: '字幕会根据实际内容自动调整\\n자막은 실제 내용에 따라 자동으로 조정됩니다' },\n      { start: 15, end: 20, text: '祝您观影愉快！\\n즐거운 시청 되세요!' },\n      { start: 20, end: 25, text: '支持多种语言字幕选择\\n다양한 언어 자막 선택을 지원합니다' },\n      { start: 25, end: 30, text: '感谢您的观看\\n시청해 주셔서 감사합니다' }\n    ],\n    'zh': [\n      { start: 0, end: 5, text: '欢迎观看这部精彩的电影' },\n      { start: 5, end: 10, text: '这是中文字幕演示' },\n      { start: 10, end: 15, text: '字幕会根据实际内容自动调整' },\n      { start: 15, end: 20, text: '祝您观影愉快！' },\n      { start: 20, end: 25, text: '支持多种语言字幕选择' },\n      { start: 25, end: 30, text: '感谢您的观看' }\n    ],\n    'ko': [\n      { start: 0, end: 5, text: '이 멋진 영화를 시청해 주셔서 환영합니다' },\n      { start: 5, end: 10, text: '이것은 한국어 자막 데모입니다' },\n      { start: 10, end: 15, text: '자막은 실제 내용에 따라 자동으로 조정됩니다' },\n      { start: 15, end: 20, text: '즐거운 시청 되세요!' },\n      { start: 20, end: 25, text: '다양한 언어 자막 선택을 지원합니다' },\n      { start: 25, end: 30, text: '시청해 주셔서 감사합니다' }\n    ]\n  }\n\n  const subtitleTracks = [\n    { id: 'none', label: '无字幕' },\n    { id: 'zh-ko', label: '中韩字幕 (中文+韩文)' },\n    { id: 'zh', label: '中文字幕' },\n    { id: 'ko', label: '한국어 (韩文)' }\n  ]\n\n  // 处理时间更新，显示自定义字幕\n  const handleTimeUpdate = () => {\n    const video = videoRef.current\n    if (!video || !showCustomSubtitle) return\n\n    const currentTime = video.currentTime\n    const subtitles = subtitleData[selectedSubtitle as keyof typeof subtitleData] || []\n    \n    const currentSubtitle = subtitles.find(sub => \n      currentTime >= sub.start && currentTime <= sub.end\n    )\n\n    setCurrentSubtitleText(currentSubtitle ? currentSubtitle.text : '')\n  }\n\n  // 切换字幕方式\n  const handleSubtitleModeChange = (useCustom: boolean) => {\n    setShowCustomSubtitle(useCustom)\n    if (!useCustom) {\n      setCurrentSubtitleText('')\n    }\n  }\n\n  // 切换字幕语言\n  const handleSubtitleChange = (subtitleId: string) => {\n    setSelectedSubtitle(subtitleId)\n    if (subtitleId === 'none') {\n      setCurrentSubtitleText('')\n    }\n  }\n\n  const togglePlay = () => {\n    const video = videoRef.current\n    if (!video) return\n\n    if (isPlaying) {\n      video.pause()\n    } else {\n      video.play()\n    }\n  }\n\n  useEffect(() => {\n    const video = videoRef.current\n    if (!video) return\n\n    video.addEventListener('timeupdate', handleTimeUpdate)\n    return () => video.removeEventListener('timeupdate', handleTimeUpdate)\n  }, [selectedSubtitle, showCustomSubtitle])\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">最终字幕解决方案测试</h1>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* 视频播放器 */}\n          <div className=\"lg:col-span-2 space-y-4\">\n            <div className=\"bg-black rounded-lg overflow-hidden relative\">\n              <video\n                ref={videoRef}\n                className=\"w-full h-auto\"\n                onPlay={() => setIsPlaying(true)}\n                onPause={() => setIsPlaying(false)}\n                crossOrigin=\"anonymous\"\n                controls\n              >\n                <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n                \n                {/* 原生字幕轨道 */}\n                {!showCustomSubtitle && (\n                  <>\n                    <track\n                      kind=\"subtitles\"\n                      src=\"/api/subtitles/test_simple.vtt\"\n                      srcLang=\"zh\"\n                      label=\"测试字幕\"\n                      default={selectedSubtitle === 'zh-ko'}\n                    />\n                    <track\n                      kind=\"subtitles\"\n                      src=\"/api/subtitles/default_zh.vtt\"\n                      srcLang=\"zh\"\n                      label=\"中文字幕\"\n                      default={selectedSubtitle === 'zh'}\n                    />\n                  </>\n                )}\n                \n                您的浏览器不支持视频播放。\n              </video>\n\n              {/* 自定义字幕覆盖层 */}\n              {showCustomSubtitle && currentSubtitleText && selectedSubtitle !== 'none' && (\n                <div className=\"absolute bottom-4 left-0 right-0 flex justify-center px-4\">\n                  <div className=\"bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg text-center max-w-4xl\">\n                    <div \n                      className=\"text-lg font-bold leading-relaxed whitespace-pre-line\"\n                      style={{\n                        textShadow: '2px 2px 4px rgba(0, 0, 0, 0.8)',\n                        lineHeight: selectedSubtitle === 'zh-ko' ? '1.3' : '1.5'\n                      }}\n                    >\n                      {currentSubtitleText}\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* 控制按钮 */}\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={togglePlay}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                {isPlaying ? '暂停' : '播放'}\n              </button>\n            </div>\n          </div>\n\n          {/* 控制面板 */}\n          <div className=\"space-y-6\">\n            {/* 字幕显示方式 */}\n            <div className=\"bg-gray-800 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-3\">字幕显示方式</h3>\n              <div className=\"space-y-2\">\n                <label className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"radio\"\n                    name=\"subtitleMode\"\n                    checked={!showCustomSubtitle}\n                    onChange={() => handleSubtitleModeChange(false)}\n                    className=\"text-blue-600\"\n                  />\n                  <span className=\"text-gray-300\">原生HTML字幕</span>\n                </label>\n                <label className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"radio\"\n                    name=\"subtitleMode\"\n                    checked={showCustomSubtitle}\n                    onChange={() => handleSubtitleModeChange(true)}\n                    className=\"text-blue-600\"\n                  />\n                  <span className=\"text-gray-300\">自定义覆盖字幕</span>\n                </label>\n              </div>\n            </div>\n\n            {/* 字幕语言选择 */}\n            <div className=\"bg-gray-800 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-3\">字幕语言</h3>\n              <div className=\"space-y-2\">\n                {subtitleTracks.map((track) => (\n                  <button\n                    key={track.id}\n                    onClick={() => handleSubtitleChange(track.id)}\n                    className={`w-full p-2 rounded text-left transition-colors ${\n                      selectedSubtitle === track.id\n                        ? 'bg-red-600 text-white'\n                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                    }`}\n                  >\n                    {track.label}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* 当前状态 */}\n            <div className=\"bg-gray-800 rounded-lg p-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-3\">当前状态</h3>\n              <div className=\"text-gray-300 text-sm space-y-1\">\n                <p>显示方式: {showCustomSubtitle ? '自定义覆盖' : '原生HTML'}</p>\n                <p>选中语言: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>\n                <p>播放状态: {isPlaying ? '播放中' : '暂停'}</p>\n                {showCustomSubtitle && currentSubtitleText && (\n                  <div className=\"mt-2 p-2 bg-gray-700 rounded\">\n                    <p className=\"text-yellow-400 text-xs\">当前字幕:</p>\n                    <p className=\"text-white text-xs whitespace-pre-line\">{currentSubtitleText}</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-8 bg-gray-800 rounded-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-white mb-2\">解决方案说明</h3>\n          <div className=\"text-gray-300 text-sm space-y-2\">\n            <p><strong>原生HTML字幕</strong>: 使用浏览器内置的字幕功能，依赖浏览器渲染</p>\n            <p><strong>自定义覆盖字幕</strong>: 使用JavaScript和CSS覆盖层显示字幕，完全可控</p>\n            <p><strong>推荐</strong>: 如果原生字幕不显示，使用自定义覆盖字幕作为备选方案</p>\n            <p><strong>优势</strong>: 自定义字幕可以确保在所有浏览器中正常显示，样式完全可控</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;QAyNE;;IAxNxB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,OAAO;IACP,MAAM,eAAe;QACnB,SAAS;YACP;gBAAE,OAAO;gBAAG,KAAK;gBAAG,MAAM;YAAsC;YAChE;gBAAE,OAAO;gBAAG,KAAK;gBAAI,MAAM;YAAsC;YACjE;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAA0C;YACtE;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAuB;YACnD;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAkC;YAC9D;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAwB;SACrD;QACD,MAAM;YACJ;gBAAE,OAAO;gBAAG,KAAK;gBAAG,MAAM;YAAc;YACxC;gBAAE,OAAO;gBAAG,KAAK;gBAAI,MAAM;YAAW;YACtC;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAgB;YAC5C;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAU;YACtC;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAa;YACzC;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAS;SACtC;QACD,MAAM;YACJ;gBAAE,OAAO;gBAAG,KAAK;gBAAG,MAAM;YAAyB;YACnD;gBAAE,OAAO;gBAAG,KAAK;gBAAI,MAAM;YAAmB;YAC9C;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAA2B;YACvD;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAc;YAC1C;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAsB;YAClD;gBAAE,OAAO;gBAAI,KAAK;gBAAI,MAAM;YAAgB;SAC7C;IACH;IAEA,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAQ,OAAO;QAAM;QAC3B;YAAE,IAAI;YAAS,OAAO;QAAe;QACrC;YAAE,IAAI;YAAM,OAAO;QAAO;QAC1B;YAAE,IAAI;YAAM,OAAO;QAAW;KAC/B;IAED,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,SAAS,CAAC,oBAAoB;QAEnC,MAAM,cAAc,MAAM,WAAW;QACrC,MAAM,YAAY,YAAY,CAAC,iBAA8C,IAAI,EAAE;QAEnF,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,MACrC,eAAe,IAAI,KAAK,IAAI,eAAe,IAAI,GAAG;QAGpD,uBAAuB,kBAAkB,gBAAgB,IAAI,GAAG;IAClE;IAEA,SAAS;IACT,MAAM,2BAA2B,CAAC;QAChC,sBAAsB;QACtB,IAAI,CAAC,WAAW;YACd,uBAAuB;QACzB;IACF;IAEA,SAAS;IACT,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,IAAI,eAAe,QAAQ;YACzB,uBAAuB;QACzB;IACF;IAEA,MAAM,aAAa;QACjB,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,IAAI,WAAW;YACb,MAAM,KAAK;QACb,OAAO;YACL,MAAM,IAAI;QACZ;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,OAAO;YAEZ,MAAM,gBAAgB,CAAC,cAAc;YACrC;mDAAO,IAAM,MAAM,mBAAmB,CAAC,cAAc;;QACvD;0CAAG;QAAC;QAAkB;KAAmB;IAEzC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAK;4CACL,WAAU;4CACV,QAAQ,IAAM,aAAa;4CAC3B,SAAS,IAAM,aAAa;4CAC5B,aAAY;4CACZ,QAAQ;;8DAER,6LAAC;oDAAO,KAAI;oDAA0C,MAAK;;;;;;gDAG1D,CAAC,oCACA;;sEACE,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,SAAQ;4DACR,OAAM;4DACN,SAAS,qBAAqB;;;;;;sEAEhC,6LAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,SAAQ;4DACR,OAAM;4DACN,SAAS,qBAAqB;;;;;;;;gDAGlC;;;;;;;wCAMH,sBAAsB,uBAAuB,qBAAqB,wBACjE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,YAAY;wDACZ,YAAY,qBAAqB,UAAU,QAAQ;oDACrD;8DAEC;;;;;;;;;;;;;;;;;;;;;;8CAQX,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAET,YAAY,OAAO;;;;;;;;;;;;;;;;;sCAM1B,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS,CAAC;4DACV,UAAU,IAAM,yBAAyB;4DACzC,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;8DAElC,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,SAAS;4DACT,UAAU,IAAM,yBAAyB;4DACzC,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;8CAMtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;oDAEC,SAAS,IAAM,qBAAqB,MAAM,EAAE;oDAC5C,WAAW,AAAC,kDAIX,OAHC,qBAAqB,MAAM,EAAE,GACzB,0BACA;8DAGL,MAAM,KAAK;mDARP,MAAM,EAAE;;;;;;;;;;;;;;;;8CAerB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;wDAAO,qBAAqB,UAAU;;;;;;;8DACzC,6LAAC;;wDAAE;yDAAO,uBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAAlC,2CAAA,qBAAqD,KAAK;;;;;;;8DACpE,6LAAC;;wDAAE;wDAAO,YAAY,QAAQ;;;;;;;gDAC7B,sBAAsB,qCACrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAA0B;;;;;;sEACvC,6LAAC;4DAAE,WAAU;sEAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQnE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAiB;;;;;;;8CAC5B,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAgB;;;;;;;8CAC3B,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAW;;;;;;;8CACtB,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;GAlPwB;KAAA", "debugId": null}}]}