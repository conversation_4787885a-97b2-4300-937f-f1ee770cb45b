(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/subtitle-debug/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>SubtitleDebugPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function SubtitleDebugPage() {
    var _subtitleTracks_find;
    _s();
    const videoRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [selectedSubtitle, setSelectedSubtitle] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('zh');
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [debugInfo, setDebugInfo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [textTracks, setTextTracks] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const addDebugInfo = (message)=>{
        const timestamp = new Date().toLocaleTimeString();
        setDebugInfo((prev)=>[
                ...prev,
                "[".concat(timestamp, "] ").concat(message)
            ]);
    };
    const subtitleTracks = [
        {
            id: 'none',
            label: '无字幕',
            language: 'none',
            src: ''
        },
        {
            id: 'zh',
            label: '中文字幕',
            language: 'zh',
            src: '/subtitles/default_zh.vtt'
        },
        {
            id: 'zh-ko',
            label: '中韩字幕 (中文+韩文)',
            language: 'zh-ko',
            src: '/subtitles/default_zh-ko.vtt'
        },
        {
            id: 'zh-en',
            label: '中英字幕 (中文+英文)',
            language: 'zh-en',
            src: '/subtitles/default_zh-en.vtt'
        }
    ];
    const updateTextTracksInfo = ()=>{
        const video = videoRef.current;
        if (!video) return;
        const tracks = Array.from(video.textTracks).map((track, index)=>({
                index,
                kind: track.kind,
                label: track.label,
                language: track.language,
                mode: track.mode,
                cues: track.cues ? track.cues.length : 0
            }));
        setTextTracks(tracks);
    };
    const handleSubtitleChange = async (subtitleId)=>{
        const video = videoRef.current;
        if (!video) return;
        addDebugInfo("开始切换字幕: ".concat(subtitleId));
        // 禁用所有现有字幕轨道
        const tracks = video.textTracks;
        for(let i = 0; i < tracks.length; i++){
            tracks[i].mode = 'disabled';
            addDebugInfo("禁用轨道 ".concat(i, ": ").concat(tracks[i].label));
        }
        if (subtitleId === 'none') {
            setSelectedSubtitle('none');
            addDebugInfo('选择无字幕');
            updateTextTracksInfo();
            return;
        }
        const selectedTrack = subtitleTracks.find((track)=>track.id === subtitleId);
        if (selectedTrack && selectedTrack.src) {
            setSelectedSubtitle(subtitleId);
            addDebugInfo("选中字幕轨道: ".concat(selectedTrack.label));
            addDebugInfo("字幕文件路径: ".concat(selectedTrack.src));
            // 清除现有的字幕轨道元素
            const existingTracks = video.querySelectorAll('track');
            addDebugInfo("清除现有轨道元素: ".concat(existingTracks.length, " 个"));
            existingTracks.forEach((track, index)=>{
                addDebugInfo("移除轨道元素 ".concat(index, ": ").concat(track.label));
                track.remove();
            });
            // 创建新的字幕轨道元素
            const trackElement = document.createElement('track');
            trackElement.kind = 'subtitles';
            trackElement.src = selectedTrack.src;
            trackElement.srcLang = selectedTrack.language.split('-')[0];
            trackElement.label = selectedTrack.label;
            trackElement.default = true;
            addDebugInfo("创建新轨道元素: ".concat(trackElement.label));
            addDebugInfo("轨道语言: ".concat(trackElement.srcLang));
            // 添加事件监听器
            trackElement.addEventListener('load', ()=>{
                addDebugInfo("字幕文件加载成功: ".concat(selectedTrack.src));
                trackElement.track.mode = 'showing';
                addDebugInfo("设置轨道模式为 showing");
                updateTextTracksInfo();
            });
            trackElement.addEventListener('error', (e)=>{
                addDebugInfo("字幕文件加载失败: ".concat(e));
            });
            // 添加到video元素
            video.appendChild(trackElement);
            addDebugInfo("轨道元素已添加到视频");
            // 延迟启用字幕显示
            setTimeout(()=>{
                const textTracks = video.textTracks;
                addDebugInfo("当前文本轨道数量: ".concat(textTracks.length));
                for(let i = 0; i < textTracks.length; i++){
                    const track = textTracks[i];
                    addDebugInfo("轨道 ".concat(i, ": ").concat(track.label, ", 模式: ").concat(track.mode, ", 提示数: ").concat(track.cues ? track.cues.length : 0));
                    if (track.label === selectedTrack.label) {
                        track.mode = 'showing';
                        addDebugInfo("成功启用字幕轨道: ".concat(track.label));
                        // 检查字幕内容
                        if (track.cues && track.cues.length > 0) {
                            addDebugInfo("字幕提示数量: ".concat(track.cues.length));
                            const firstCue = track.cues[0];
                            addDebugInfo('第一个字幕: "'.concat(firstCue.text, '"'));
                        }
                        break;
                    }
                }
                updateTextTracksInfo();
            }, 500);
        }
    };
    const clearDebugInfo = ()=>{
        setDebugInfo([]);
    };
    const testSubtitleFile = async (src)=>{
        try {
            const response = await fetch(src);
            if (response.ok) {
                const content = await response.text();
                addDebugInfo("字幕文件 ".concat(src, " 可访问，内容长度: ").concat(content.length));
                addDebugInfo("文件内容预览: ".concat(content.substring(0, 100), "..."));
            } else {
                addDebugInfo("字幕文件 ".concat(src, " 访问失败: ").concat(response.status));
            }
        } catch (error) {
            addDebugInfo("字幕文件 ".concat(src, " 测试失败: ").concat(error));
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubtitleDebugPage.useEffect": ()=>{
            // 初始化
            addDebugInfo('页面初始化');
            updateTextTracksInfo();
            // 测试所有字幕文件
            subtitleTracks.forEach({
                "SubtitleDebugPage.useEffect": (track)=>{
                    if (track.src) {
                        testSubtitleFile(track.src);
                    }
                }
            }["SubtitleDebugPage.useEffect"]);
        }
    }["SubtitleDebugPage.useEffect"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-900 p-4",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-6xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-3xl font-bold text-white mb-6",
                    children: "字幕调试工具"
                }, void 0, false, {
                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                    lineNumber: 182,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-800 rounded-lg p-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-xl font-semibold text-white mb-4",
                                    children: "视频播放器"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                    lineNumber: 187,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-black rounded-lg overflow-hidden mb-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                        ref: videoRef,
                                        className: "w-full h-auto",
                                        onPlay: ()=>{
                                            setIsPlaying(true);
                                            addDebugInfo('视频开始播放');
                                        },
                                        onPause: ()=>{
                                            setIsPlaying(false);
                                            addDebugInfo('视频暂停');
                                        },
                                        onLoadedMetadata: ()=>{
                                            addDebugInfo('视频元数据加载完成');
                                            updateTextTracksInfo();
                                        },
                                        crossOrigin: "anonymous",
                                        controls: true,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("source", {
                                                src: "/uploads/movies/movie_1755665391178.mp4",
                                                type: "video/mp4"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                lineNumber: 207,
                                                columnNumber: 17
                                            }, this),
                                            "您的浏览器不支持视频播放。"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                        lineNumber: 189,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                    lineNumber: 188,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-2 gap-2 mb-4",
                                    children: subtitleTracks.map((track)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleSubtitleChange(track.id),
                                            className: "p-2 rounded text-sm font-medium transition-colors ".concat(selectedSubtitle === track.id ? 'bg-red-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'),
                                            children: track.label
                                        }, track.id, false, {
                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                            lineNumber: 215,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                    lineNumber: 213,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-gray-300 text-sm",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: [
                                                "选中字幕: ",
                                                (_subtitleTracks_find = subtitleTracks.find((t)=>t.id === selectedSubtitle)) === null || _subtitleTracks_find === void 0 ? void 0 : _subtitleTracks_find.label
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                            lineNumber: 231,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: [
                                                "播放状态: ",
                                                isPlaying ? '播放中' : '暂停'
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                            lineNumber: 232,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                    lineNumber: 230,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                            lineNumber: 186,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-800 rounded-lg p-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex justify-between items-center mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-white",
                                            children: "调试信息"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                            lineNumber: 239,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: clearDebugInfo,
                                            className: "px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",
                                            children: "清除"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                            lineNumber: 240,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                    lineNumber: 238,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-900 rounded p-3 h-64 overflow-y-auto",
                                    children: debugInfo.map((info, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-green-400 text-xs mb-1 font-mono",
                                            children: info
                                        }, index, false, {
                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                            lineNumber: 249,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                    lineNumber: 247,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                            lineNumber: 237,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                    lineNumber: 184,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-gray-800 rounded-lg p-4 mt-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-semibold text-white mb-4",
                            children: "当前文本轨道"
                        }, void 0, false, {
                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                            lineNumber: 259,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "overflow-x-auto",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                                className: "w-full text-sm text-gray-300",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            className: "border-b border-gray-600",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-left p-2",
                                                    children: "索引"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                    lineNumber: 264,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-left p-2",
                                                    children: "类型"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                    lineNumber: 265,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-left p-2",
                                                    children: "标签"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                    lineNumber: 266,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-left p-2",
                                                    children: "语言"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-left p-2",
                                                    children: "模式"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                    lineNumber: 268,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                    className: "text-left p-2",
                                                    children: "提示数"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                    lineNumber: 269,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                            lineNumber: 263,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                        lineNumber: 262,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                        children: textTracks.map((track)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                                className: "border-b border-gray-700",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-2",
                                                        children: track.index
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                        lineNumber: 275,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-2",
                                                        children: track.kind
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                        lineNumber: 276,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-2",
                                                        children: track.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                        lineNumber: 277,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-2",
                                                        children: track.language
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                        lineNumber: 278,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-1 rounded text-xs ".concat(track.mode === 'showing' ? 'bg-green-600' : track.mode === 'disabled' ? 'bg-red-600' : 'bg-gray-600'),
                                                            children: track.mode
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                            lineNumber: 280,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                        lineNumber: 279,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                        className: "p-2",
                                                        children: track.cues
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                        lineNumber: 287,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, track.index, true, {
                                                fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                                lineNumber: 274,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                        lineNumber: 272,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/subtitle-debug/page.tsx",
                                lineNumber: 261,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/subtitle-debug/page.tsx",
                            lineNumber: 260,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/subtitle-debug/page.tsx",
                    lineNumber: 258,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/subtitle-debug/page.tsx",
            lineNumber: 181,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/subtitle-debug/page.tsx",
        lineNumber: 180,
        columnNumber: 5
    }, this);
}
_s(SubtitleDebugPage, "Y3cgq2iwEI1zwdaVp9Q/LTFYo6g=");
_c = SubtitleDebugPage;
var _c;
__turbopack_context__.k.register(_c, "SubtitleDebugPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_app_subtitle-debug_page_tsx_60f14b46._.js.map