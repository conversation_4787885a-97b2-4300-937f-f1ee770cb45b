{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/subtitle-diagnosis/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useRef, useEffect, useState } from 'react'\n\nexport default function SubtitleDiagnosisPage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [diagnostics, setDiagnostics] = useState<any>({})\n  const [currentTime, setCurrentTime] = useState(0)\n\n  useEffect(() => {\n    const video = videoRef.current\n    if (!video) return\n\n    const runDiagnostics = () => {\n      const tracks = Array.from(video.textTracks)\n      const videoRect = video.getBoundingClientRect()\n      \n      const diag = {\n        videoElement: {\n          src: video.src,\n          readyState: video.readyState,\n          currentTime: video.currentTime,\n          duration: video.duration,\n          videoWidth: video.videoWidth,\n          videoHeight: video.videoHeight,\n          clientWidth: video.clientWidth,\n          clientHeight: video.clientHeight,\n          crossOrigin: video.crossOrigin\n        },\n        textTracks: tracks.map((track, index) => ({\n          index,\n          kind: track.kind,\n          label: track.label,\n          language: track.language,\n          mode: track.mode,\n          cues: track.cues ? Array.from(track.cues).map(cue => ({\n            startTime: cue.startTime,\n            endTime: cue.endTime,\n            text: cue.text,\n            id: cue.id\n          })) : [],\n          activeCues: track.activeCues ? Array.from(track.activeCues).map(cue => ({\n            startTime: cue.startTime,\n            endTime: cue.endTime,\n            text: cue.text\n          })) : []\n        })),\n        browser: {\n          userAgent: navigator.userAgent,\n          platform: navigator.platform,\n          language: navigator.language\n        },\n        styles: {\n          computedStyle: window.getComputedStyle(video),\n          videoRect: {\n            width: videoRect.width,\n            height: videoRect.height,\n            top: videoRect.top,\n            left: videoRect.left\n          }\n        }\n      }\n      \n      setDiagnostics(diag)\n    }\n\n    const handleTimeUpdate = () => {\n      setCurrentTime(video.currentTime)\n      runDiagnostics()\n    }\n\n    video.addEventListener('loadedmetadata', runDiagnostics)\n    video.addEventListener('timeupdate', handleTimeUpdate)\n    video.addEventListener('loadstart', runDiagnostics)\n    video.addEventListener('canplay', runDiagnostics)\n\n    // 强制启用所有字幕轨道\n    const enableAllTracks = () => {\n      const tracks = video.textTracks\n      for (let i = 0; i < tracks.length; i++) {\n        tracks[i].mode = 'showing'\n        console.log(`启用轨道 ${i}:`, tracks[i])\n      }\n      runDiagnostics()\n    }\n\n    setTimeout(enableAllTracks, 2000)\n\n    return () => {\n      video.removeEventListener('loadedmetadata', runDiagnostics)\n      video.removeEventListener('timeupdate', handleTimeUpdate)\n      video.removeEventListener('loadstart', runDiagnostics)\n      video.removeEventListener('canplay', runDiagnostics)\n    }\n  }, [])\n\n  const testSubtitleFile = async (url: string) => {\n    try {\n      const response = await fetch(url)\n      const text = await response.text()\n      console.log('字幕文件内容:', text)\n      return { success: true, content: text.substring(0, 500) }\n    } catch (error) {\n      console.error('字幕文件加载失败:', error)\n      return { success: false, error: String(error) }\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-6xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">字幕问题诊断页面</h1>\n        \n        <div className=\"space-y-6\">\n          {/* 视频播放器 */}\n          <div className=\"bg-black rounded-lg overflow-hidden\">\n            <video\n              ref={videoRef}\n              className=\"w-full h-auto\"\n              controls\n              crossOrigin=\"anonymous\"\n              autoPlay={false}\n            >\n              <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n              <track\n                kind=\"subtitles\"\n                src=\"/api/subtitles/test_simple.vtt\"\n                srcLang=\"zh\"\n                label=\"测试字幕\"\n                default\n              />\n            </video>\n          </div>\n\n          {/* 控制按钮 */}\n          <div className=\"flex space-x-4\">\n            <button\n              onClick={() => testSubtitleFile('/api/subtitles/test_simple.vtt')}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n            >\n              测试字幕文件\n            </button>\n            <button\n              onClick={() => {\n                const video = videoRef.current\n                if (video) {\n                  for (let i = 0; i < video.textTracks.length; i++) {\n                    video.textTracks[i].mode = 'showing'\n                  }\n                }\n              }}\n              className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n            >\n              强制启用字幕\n            </button>\n          </div>\n\n          {/* 诊断信息 */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* 视频信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">视频元素信息</h2>\n              {diagnostics.videoElement && (\n                <div className=\"text-sm text-gray-300 space-y-1\">\n                  <p>源文件: {diagnostics.videoElement.src}</p>\n                  <p>就绪状态: {diagnostics.videoElement.readyState}</p>\n                  <p>当前时间: {diagnostics.videoElement.currentTime?.toFixed(2)}s</p>\n                  <p>总时长: {diagnostics.videoElement.duration?.toFixed(2)}s</p>\n                  <p>视频尺寸: {diagnostics.videoElement.videoWidth} x {diagnostics.videoElement.videoHeight}</p>\n                  <p>显示尺寸: {diagnostics.videoElement.clientWidth} x {diagnostics.videoElement.clientHeight}</p>\n                  <p>跨域设置: {diagnostics.videoElement.crossOrigin || '无'}</p>\n                </div>\n              )}\n            </div>\n\n            {/* 字幕轨道信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">字幕轨道信息</h2>\n              {diagnostics.textTracks && diagnostics.textTracks.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {diagnostics.textTracks.map((track: any) => (\n                    <div key={track.index} className=\"bg-gray-700 rounded p-3\">\n                      <h3 className=\"font-medium text-white\">轨道 {track.index}: {track.label}</h3>\n                      <div className=\"text-xs text-gray-300 mt-2 space-y-1\">\n                        <p>类型: {track.kind}</p>\n                        <p>语言: {track.language}</p>\n                        <p>模式: <span className={track.mode === 'showing' ? 'text-green-400' : 'text-red-400'}>{track.mode}</span></p>\n                        <p>字幕条目数: {track.cues.length}</p>\n                        <p>活跃字幕数: {track.activeCues.length}</p>\n                        \n                        {track.activeCues.length > 0 && (\n                          <div className=\"mt-2 p-2 bg-gray-600 rounded\">\n                            <p className=\"text-yellow-400 font-medium\">当前活跃字幕:</p>\n                            {track.activeCues.map((cue: any, index: number) => (\n                              <p key={index} className=\"text-white whitespace-pre-line\">{cue.text}</p>\n                            ))}\n                          </div>\n                        )}\n                        \n                        {track.cues.length > 0 && (\n                          <details className=\"mt-2\">\n                            <summary className=\"text-blue-400 cursor-pointer\">查看所有字幕条目</summary>\n                            <div className=\"mt-2 max-h-40 overflow-y-auto\">\n                              {track.cues.map((cue: any, index: number) => (\n                                <div key={index} className=\"text-xs p-1 border-b border-gray-600\">\n                                  <p>{cue.startTime.toFixed(2)}s - {cue.endTime.toFixed(2)}s</p>\n                                  <p className=\"text-gray-200\">{cue.text}</p>\n                                </div>\n                              ))}\n                            </div>\n                          </details>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-400\">没有检测到字幕轨道</p>\n              )}\n            </div>\n\n            {/* 浏览器信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">浏览器信息</h2>\n              {diagnostics.browser && (\n                <div className=\"text-sm text-gray-300 space-y-1\">\n                  <p>用户代理: {diagnostics.browser.userAgent}</p>\n                  <p>平台: {diagnostics.browser.platform}</p>\n                  <p>语言: {diagnostics.browser.language}</p>\n                </div>\n              )}\n            </div>\n\n            {/* 样式信息 */}\n            <div className=\"bg-gray-800 rounded-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-white mb-4\">样式信息</h2>\n              {diagnostics.styles && (\n                <div className=\"text-sm text-gray-300 space-y-1\">\n                  <p>视频位置: {diagnostics.styles.videoRect?.left}, {diagnostics.styles.videoRect?.top}</p>\n                  <p>视频大小: {diagnostics.styles.videoRect?.width} x {diagnostics.styles.videoRect?.height}</p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* 说明 */}\n          <div className=\"bg-gray-800 rounded-lg p-6\">\n            <h2 className=\"text-xl font-semibold text-white mb-4\">诊断说明</h2>\n            <div className=\"text-gray-300 text-sm space-y-2\">\n              <p>1. 检查视频元素是否正确加载</p>\n              <p>2. 检查字幕轨道是否正确添加和启用</p>\n              <p>3. 检查字幕条目是否正确解析</p>\n              <p>4. 检查当前时间是否有活跃的字幕</p>\n              <p>5. 如果有活跃字幕但视频上不显示，可能是CSS样式问题</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;QAkKI,uCACD,oCAuEC,+BAAsC,gCACtC,gCAAwC;;IA1OlE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,QAAQ,SAAS,OAAO;YAC9B,IAAI,CAAC,OAAO;YAEZ,MAAM;kEAAiB;oBACrB,MAAM,SAAS,MAAM,IAAI,CAAC,MAAM,UAAU;oBAC1C,MAAM,YAAY,MAAM,qBAAqB;oBAE7C,MAAM,OAAO;wBACX,cAAc;4BACZ,KAAK,MAAM,GAAG;4BACd,YAAY,MAAM,UAAU;4BAC5B,aAAa,MAAM,WAAW;4BAC9B,UAAU,MAAM,QAAQ;4BACxB,YAAY,MAAM,UAAU;4BAC5B,aAAa,MAAM,WAAW;4BAC9B,aAAa,MAAM,WAAW;4BAC9B,cAAc,MAAM,YAAY;4BAChC,aAAa,MAAM,WAAW;wBAChC;wBACA,YAAY,OAAO,GAAG;8EAAC,CAAC,OAAO,QAAU,CAAC;oCACxC;oCACA,MAAM,MAAM,IAAI;oCAChB,OAAO,MAAM,KAAK;oCAClB,UAAU,MAAM,QAAQ;oCACxB,MAAM,MAAM,IAAI;oCAChB,MAAM,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,IAAI,EAAE,GAAG;0FAAC,CAAA,MAAO,CAAC;gDACpD,WAAW,IAAI,SAAS;gDACxB,SAAS,IAAI,OAAO;gDACpB,MAAM,IAAI,IAAI;gDACd,IAAI,IAAI,EAAE;4CACZ,CAAC;2FAAK,EAAE;oCACR,YAAY,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,UAAU,EAAE,GAAG;0FAAC,CAAA,MAAO,CAAC;gDACtE,WAAW,IAAI,SAAS;gDACxB,SAAS,IAAI,OAAO;gDACpB,MAAM,IAAI,IAAI;4CAChB,CAAC;2FAAK,EAAE;gCACV,CAAC;;wBACD,SAAS;4BACP,WAAW,UAAU,SAAS;4BAC9B,UAAU,UAAU,QAAQ;4BAC5B,UAAU,UAAU,QAAQ;wBAC9B;wBACA,QAAQ;4BACN,eAAe,OAAO,gBAAgB,CAAC;4BACvC,WAAW;gCACT,OAAO,UAAU,KAAK;gCACtB,QAAQ,UAAU,MAAM;gCACxB,KAAK,UAAU,GAAG;gCAClB,MAAM,UAAU,IAAI;4BACtB;wBACF;oBACF;oBAEA,eAAe;gBACjB;;YAEA,MAAM;oEAAmB;oBACvB,eAAe,MAAM,WAAW;oBAChC;gBACF;;YAEA,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,aAAa;YACpC,MAAM,gBAAgB,CAAC,WAAW;YAElC,aAAa;YACb,MAAM;mEAAkB;oBACtB,MAAM,SAAS,MAAM,UAAU;oBAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;wBACjB,QAAQ,GAAG,CAAC,AAAC,QAAS,OAAF,GAAE,MAAI,MAAM,CAAC,EAAE;oBACrC;oBACA;gBACF;;YAEA,WAAW,iBAAiB;YAE5B;mDAAO;oBACL,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,aAAa;oBACvC,MAAM,mBAAmB,CAAC,WAAW;gBACvC;;QACF;0CAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,WAAW;YACvB,OAAO;gBAAE,SAAS;gBAAM,SAAS,KAAK,SAAS,CAAC,GAAG;YAAK;QAC1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO;gBAAE,SAAS;gBAAO,OAAO,OAAO;YAAO;QAChD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,QAAQ;gCACR,aAAY;gCACZ,UAAU;;kDAEV,6LAAC;wCAAO,KAAI;wCAA0C,MAAK;;;;;;kDAC3D,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,SAAQ;wCACR,OAAM;wCACN,OAAO;;;;;;;;;;;;;;;;;sCAMb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;wCACP,MAAM,QAAQ,SAAS,OAAO;wCAC9B,IAAI,OAAO;4CACT,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,UAAU,CAAC,MAAM,EAAE,IAAK;gDAChD,MAAM,UAAU,CAAC,EAAE,CAAC,IAAI,GAAG;4CAC7B;wCACF;oCACF;oCACA,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,YAAY,kBACvB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;wDAAM,YAAY,YAAY,CAAC,GAAG;;;;;;;8DACrC,6LAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,UAAU;;;;;;;8DAC7C,6LAAC;;wDAAE;yDAAO,wCAAA,YAAY,YAAY,CAAC,WAAW,cAApC,4DAAA,sCAAsC,OAAO,CAAC;wDAAG;;;;;;;8DAC3D,6LAAC;;wDAAE;yDAAM,qCAAA,YAAY,YAAY,CAAC,QAAQ,cAAjC,yDAAA,mCAAmC,OAAO,CAAC;wDAAG;;;;;;;8DACvD,6LAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,UAAU;wDAAC;wDAAI,YAAY,YAAY,CAAC,WAAW;;;;;;;8DACtF,6LAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,WAAW;wDAAC;wDAAI,YAAY,YAAY,CAAC,YAAY;;;;;;;8DACxF,6LAAC;;wDAAE;wDAAO,YAAY,YAAY,CAAC,WAAW,IAAI;;;;;;;;;;;;;;;;;;;8CAMxD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,UAAU,IAAI,YAAY,UAAU,CAAC,MAAM,GAAG,kBACzD,6LAAC;4CAAI,WAAU;sDACZ,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,sBAC3B,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC;4DAAG,WAAU;;gEAAyB;gEAAI,MAAM,KAAK;gEAAC;gEAAG,MAAM,KAAK;;;;;;;sEACrE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAE;wEAAK,MAAM,IAAI;;;;;;;8EAClB,6LAAC;;wEAAE;wEAAK,MAAM,QAAQ;;;;;;;8EACtB,6LAAC;;wEAAE;sFAAI,6LAAC;4EAAK,WAAW,MAAM,IAAI,KAAK,YAAY,mBAAmB;sFAAiB,MAAM,IAAI;;;;;;;;;;;;8EACjG,6LAAC;;wEAAE;wEAAQ,MAAM,IAAI,CAAC,MAAM;;;;;;;8EAC5B,6LAAC;;wEAAE;wEAAQ,MAAM,UAAU,CAAC,MAAM;;;;;;;gEAEjC,MAAM,UAAU,CAAC,MAAM,GAAG,mBACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAA8B;;;;;;wEAC1C,MAAM,UAAU,CAAC,GAAG,CAAC,CAAC,KAAU,sBAC/B,6LAAC;gFAAc,WAAU;0FAAkC,IAAI,IAAI;+EAA3D;;;;;;;;;;;gEAKb,MAAM,IAAI,CAAC,MAAM,GAAG,mBACnB,6LAAC;oEAAQ,WAAU;;sFACjB,6LAAC;4EAAQ,WAAU;sFAA+B;;;;;;sFAClD,6LAAC;4EAAI,WAAU;sFACZ,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,sBACzB,6LAAC;oFAAgB,WAAU;;sGACzB,6LAAC;;gGAAG,IAAI,SAAS,CAAC,OAAO,CAAC;gGAAG;gGAAK,IAAI,OAAO,CAAC,OAAO,CAAC;gGAAG;;;;;;;sGACzD,6LAAC;4FAAE,WAAU;sGAAiB,IAAI,IAAI;;;;;;;mFAF9B;;;;;;;;;;;;;;;;;;;;;;;mDAvBZ,MAAM,KAAK;;;;;;;;;iEAoCzB,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAKjC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,OAAO,kBAClB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;wDAAO,YAAY,OAAO,CAAC,SAAS;;;;;;;8DACvC,6LAAC;;wDAAE;wDAAK,YAAY,OAAO,CAAC,QAAQ;;;;;;;8DACpC,6LAAC;;wDAAE;wDAAK,YAAY,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;8CAM1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;wCACrD,YAAY,MAAM,kBACjB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAE;yDAAO,gCAAA,YAAY,MAAM,CAAC,SAAS,cAA5B,oDAAA,8BAA8B,IAAI;wDAAC;yDAAG,iCAAA,YAAY,MAAM,CAAC,SAAS,cAA5B,qDAAA,+BAA8B,GAAG;;;;;;;8DACjF,6LAAC;;wDAAE;yDAAO,iCAAA,YAAY,MAAM,CAAC,SAAS,cAA5B,qDAAA,+BAA8B,KAAK;wDAAC;yDAAI,iCAAA,YAAY,MAAM,CAAC,SAAS,cAA5B,qDAAA,+BAA8B,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;sCAO9F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GAhQwB;KAAA", "debugId": null}}]}