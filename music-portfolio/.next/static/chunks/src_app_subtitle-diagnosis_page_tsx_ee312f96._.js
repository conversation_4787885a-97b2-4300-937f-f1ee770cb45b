(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/app/subtitle-diagnosis/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>SubtitleDiagnosisPage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
function SubtitleDiagnosisPage() {
    var _diagnostics_videoElement_currentTime, _diagnostics_videoElement_duration, _diagnostics_styles_videoRect, _diagnostics_styles_videoRect1, _diagnostics_styles_videoRect2, _diagnostics_styles_videoRect3;
    _s();
    const videoRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [diagnostics, setDiagnostics] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [currentTime, setCurrentTime] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SubtitleDiagnosisPage.useEffect": ()=>{
            const video = videoRef.current;
            if (!video) return;
            const runDiagnostics = {
                "SubtitleDiagnosisPage.useEffect.runDiagnostics": ()=>{
                    const tracks = Array.from(video.textTracks);
                    const videoRect = video.getBoundingClientRect();
                    const diag = {
                        videoElement: {
                            src: video.src,
                            readyState: video.readyState,
                            currentTime: video.currentTime,
                            duration: video.duration,
                            videoWidth: video.videoWidth,
                            videoHeight: video.videoHeight,
                            clientWidth: video.clientWidth,
                            clientHeight: video.clientHeight,
                            crossOrigin: video.crossOrigin
                        },
                        textTracks: tracks.map({
                            "SubtitleDiagnosisPage.useEffect.runDiagnostics": (track, index)=>({
                                    index,
                                    kind: track.kind,
                                    label: track.label,
                                    language: track.language,
                                    mode: track.mode,
                                    cues: track.cues ? Array.from(track.cues).map({
                                        "SubtitleDiagnosisPage.useEffect.runDiagnostics": (cue)=>({
                                                startTime: cue.startTime,
                                                endTime: cue.endTime,
                                                text: cue.text,
                                                id: cue.id
                                            })
                                    }["SubtitleDiagnosisPage.useEffect.runDiagnostics"]) : [],
                                    activeCues: track.activeCues ? Array.from(track.activeCues).map({
                                        "SubtitleDiagnosisPage.useEffect.runDiagnostics": (cue)=>({
                                                startTime: cue.startTime,
                                                endTime: cue.endTime,
                                                text: cue.text
                                            })
                                    }["SubtitleDiagnosisPage.useEffect.runDiagnostics"]) : []
                                })
                        }["SubtitleDiagnosisPage.useEffect.runDiagnostics"]),
                        browser: {
                            userAgent: navigator.userAgent,
                            platform: navigator.platform,
                            language: navigator.language
                        },
                        styles: {
                            computedStyle: window.getComputedStyle(video),
                            videoRect: {
                                width: videoRect.width,
                                height: videoRect.height,
                                top: videoRect.top,
                                left: videoRect.left
                            }
                        }
                    };
                    setDiagnostics(diag);
                }
            }["SubtitleDiagnosisPage.useEffect.runDiagnostics"];
            const handleTimeUpdate = {
                "SubtitleDiagnosisPage.useEffect.handleTimeUpdate": ()=>{
                    setCurrentTime(video.currentTime);
                    runDiagnostics();
                }
            }["SubtitleDiagnosisPage.useEffect.handleTimeUpdate"];
            video.addEventListener('loadedmetadata', runDiagnostics);
            video.addEventListener('timeupdate', handleTimeUpdate);
            video.addEventListener('loadstart', runDiagnostics);
            video.addEventListener('canplay', runDiagnostics);
            // 强制启用所有字幕轨道
            const enableAllTracks = {
                "SubtitleDiagnosisPage.useEffect.enableAllTracks": ()=>{
                    const tracks = video.textTracks;
                    for(let i = 0; i < tracks.length; i++){
                        tracks[i].mode = 'showing';
                        console.log("启用轨道 ".concat(i, ":"), tracks[i]);
                    }
                    runDiagnostics();
                }
            }["SubtitleDiagnosisPage.useEffect.enableAllTracks"];
            setTimeout(enableAllTracks, 2000);
            return ({
                "SubtitleDiagnosisPage.useEffect": ()=>{
                    video.removeEventListener('loadedmetadata', runDiagnostics);
                    video.removeEventListener('timeupdate', handleTimeUpdate);
                    video.removeEventListener('loadstart', runDiagnostics);
                    video.removeEventListener('canplay', runDiagnostics);
                }
            })["SubtitleDiagnosisPage.useEffect"];
        }
    }["SubtitleDiagnosisPage.useEffect"], []);
    const testSubtitleFile = async (url)=>{
        try {
            const response = await fetch(url);
            const text = await response.text();
            console.log('字幕文件内容:', text);
            return {
                success: true,
                content: text.substring(0, 500)
            };
        } catch (error) {
            console.error('字幕文件加载失败:', error);
            return {
                success: false,
                error: String(error)
            };
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-900 p-8",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-6xl mx-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-3xl font-bold text-white mb-8",
                    children: "字幕问题诊断页面"
                }, void 0, false, {
                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                    lineNumber: 112,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-black rounded-lg overflow-hidden",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("video", {
                                ref: videoRef,
                                className: "w-full h-auto",
                                controls: true,
                                crossOrigin: "anonymous",
                                autoPlay: false,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("source", {
                                        src: "/uploads/movies/movie_1755665391178.mp4",
                                        type: "video/mp4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                        lineNumber: 124,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("track", {
                                        kind: "subtitles",
                                        src: "/api/subtitles/test_simple.vtt",
                                        srcLang: "zh",
                                        label: "测试字幕",
                                        default: true
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                        lineNumber: 125,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                lineNumber: 117,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                            lineNumber: 116,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex space-x-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>testSubtitleFile('/api/subtitles/test_simple.vtt'),
                                    className: "px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",
                                    children: "测试字幕文件"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 137,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        const video = videoRef.current;
                                        if (video) {
                                            for(let i = 0; i < video.textTracks.length; i++){
                                                video.textTracks[i].mode = 'showing';
                                            }
                                        }
                                    },
                                    className: "px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",
                                    children: "强制启用字幕"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 143,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                            lineNumber: 136,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-white mb-4",
                                            children: "视频元素信息"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 162,
                                            columnNumber: 15
                                        }, this),
                                        diagnostics.videoElement && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-gray-300 space-y-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "源文件: ",
                                                        diagnostics.videoElement.src
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 165,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "就绪状态: ",
                                                        diagnostics.videoElement.readyState
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 166,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "当前时间: ",
                                                        (_diagnostics_videoElement_currentTime = diagnostics.videoElement.currentTime) === null || _diagnostics_videoElement_currentTime === void 0 ? void 0 : _diagnostics_videoElement_currentTime.toFixed(2),
                                                        "s"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 167,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "总时长: ",
                                                        (_diagnostics_videoElement_duration = diagnostics.videoElement.duration) === null || _diagnostics_videoElement_duration === void 0 ? void 0 : _diagnostics_videoElement_duration.toFixed(2),
                                                        "s"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 168,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "视频尺寸: ",
                                                        diagnostics.videoElement.videoWidth,
                                                        " x ",
                                                        diagnostics.videoElement.videoHeight
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 169,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "显示尺寸: ",
                                                        diagnostics.videoElement.clientWidth,
                                                        " x ",
                                                        diagnostics.videoElement.clientHeight
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "跨域设置: ",
                                                        diagnostics.videoElement.crossOrigin || '无'
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 171,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 164,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 161,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-white mb-4",
                                            children: "字幕轨道信息"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 178,
                                            columnNumber: 15
                                        }, this),
                                        diagnostics.textTracks && diagnostics.textTracks.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "space-y-4",
                                            children: diagnostics.textTracks.map((track)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "bg-gray-700 rounded p-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "font-medium text-white",
                                                            children: [
                                                                "轨道 ",
                                                                track.index,
                                                                ": ",
                                                                track.label
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                            lineNumber: 183,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs text-gray-300 mt-2 space-y-1",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    children: [
                                                                        "类型: ",
                                                                        track.kind
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                    lineNumber: 185,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    children: [
                                                                        "语言: ",
                                                                        track.language
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                    lineNumber: 186,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    children: [
                                                                        "模式: ",
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: track.mode === 'showing' ? 'text-green-400' : 'text-red-400',
                                                                            children: track.mode
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                            lineNumber: 187,
                                                                            columnNumber: 32
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                    lineNumber: 187,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    children: [
                                                                        "字幕条目数: ",
                                                                        track.cues.length
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                    lineNumber: 188,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                    children: [
                                                                        "活跃字幕数: ",
                                                                        track.activeCues.length
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                    lineNumber: 189,
                                                                    columnNumber: 25
                                                                }, this),
                                                                track.activeCues.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "mt-2 p-2 bg-gray-600 rounded",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-yellow-400 font-medium",
                                                                            children: "当前活跃字幕:"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                            lineNumber: 193,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        track.activeCues.map((cue, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                className: "text-white whitespace-pre-line",
                                                                                children: cue.text
                                                                            }, index, false, {
                                                                                fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                                lineNumber: 195,
                                                                                columnNumber: 31
                                                                            }, this))
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                    lineNumber: 192,
                                                                    columnNumber: 27
                                                                }, this),
                                                                track.cues.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                                                                    className: "mt-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                                                            className: "text-blue-400 cursor-pointer",
                                                                            children: "查看所有字幕条目"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                            lineNumber: 202,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "mt-2 max-h-40 overflow-y-auto",
                                                                            children: track.cues.map((cue, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                    className: "text-xs p-1 border-b border-gray-600",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                            children: [
                                                                                                cue.startTime.toFixed(2),
                                                                                                "s - ",
                                                                                                cue.endTime.toFixed(2),
                                                                                                "s"
                                                                                            ]
                                                                                        }, void 0, true, {
                                                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                                            lineNumber: 206,
                                                                                            columnNumber: 35
                                                                                        }, this),
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                                            className: "text-gray-200",
                                                                                            children: cue.text
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                                            lineNumber: 207,
                                                                                            columnNumber: 35
                                                                                        }, this)
                                                                                    ]
                                                                                }, index, true, {
                                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                                    lineNumber: 205,
                                                                                    columnNumber: 33
                                                                                }, this))
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                            lineNumber: 203,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                                    lineNumber: 201,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                            lineNumber: 184,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, track.index, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 182,
                                                    columnNumber: 21
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 180,
                                            columnNumber: 17
                                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-400",
                                            children: "没有检测到字幕轨道"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 218,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 177,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-white mb-4",
                                            children: "浏览器信息"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 224,
                                            columnNumber: 15
                                        }, this),
                                        diagnostics.browser && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-gray-300 space-y-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "用户代理: ",
                                                        diagnostics.browser.userAgent
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 227,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "平台: ",
                                                        diagnostics.browser.platform
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 228,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "语言: ",
                                                        diagnostics.browser.language
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 229,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 226,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 223,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-800 rounded-lg p-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-white mb-4",
                                            children: "样式信息"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 236,
                                            columnNumber: 15
                                        }, this),
                                        diagnostics.styles && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-sm text-gray-300 space-y-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "视频位置: ",
                                                        (_diagnostics_styles_videoRect = diagnostics.styles.videoRect) === null || _diagnostics_styles_videoRect === void 0 ? void 0 : _diagnostics_styles_videoRect.left,
                                                        ", ",
                                                        (_diagnostics_styles_videoRect1 = diagnostics.styles.videoRect) === null || _diagnostics_styles_videoRect1 === void 0 ? void 0 : _diagnostics_styles_videoRect1.top
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 239,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    children: [
                                                        "视频大小: ",
                                                        (_diagnostics_styles_videoRect2 = diagnostics.styles.videoRect) === null || _diagnostics_styles_videoRect2 === void 0 ? void 0 : _diagnostics_styles_videoRect2.width,
                                                        " x ",
                                                        (_diagnostics_styles_videoRect3 = diagnostics.styles.videoRect) === null || _diagnostics_styles_videoRect3 === void 0 ? void 0 : _diagnostics_styles_videoRect3.height
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                                    lineNumber: 240,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 238,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 235,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                            lineNumber: 159,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gray-800 rounded-lg p-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: "text-xl font-semibold text-white mb-4",
                                    children: "诊断说明"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 248,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-gray-300 text-sm space-y-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "1. 检查视频元素是否正确加载"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 250,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "2. 检查字幕轨道是否正确添加和启用"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 251,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "3. 检查字幕条目是否正确解析"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 252,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "4. 检查当前时间是否有活跃的字幕"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 253,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "5. 如果有活跃字幕但视频上不显示，可能是CSS样式问题"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                            lineNumber: 254,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                                    lineNumber: 249,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                            lineNumber: 247,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
                    lineNumber: 114,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
            lineNumber: 111,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/subtitle-diagnosis/page.tsx",
        lineNumber: 110,
        columnNumber: 5
    }, this);
}
_s(SubtitleDiagnosisPage, "N0VPhwJgHBi7xmP9Uf5He08xDDI=");
_c = SubtitleDiagnosisPage;
var _c;
__turbopack_context__.k.register(_c, "SubtitleDiagnosisPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_app_subtitle-diagnosis_page_tsx_ee312f96._.js.map