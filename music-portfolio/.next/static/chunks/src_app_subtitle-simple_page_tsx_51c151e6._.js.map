{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/subtitle-simple/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\n\nexport default function SubtitleSimplePage() {\n  const videoRef = useRef<HTMLVideoElement>(null)\n  const [selectedSubtitle, setSelectedSubtitle] = useState('zh')\n\n  const subtitleTracks = [\n    {\n      id: 'none',\n      label: '无字幕',\n      src: ''\n    },\n    {\n      id: 'zh',\n      label: '中文字幕',\n      src: '/subtitles/default_zh.vtt'\n    },\n    {\n      id: 'zh-ko',\n      label: '中韩字幕',\n      src: '/subtitles/default_zh-ko.vtt'\n    },\n    {\n      id: 'zh-en',\n      label: '中英字幕',\n      src: '/subtitles/default_zh-en.vtt'\n    }\n  ]\n\n  const handleSubtitleChange = (subtitleId: string) => {\n    const video = videoRef.current\n    if (!video) return\n\n    console.log('切换字幕:', subtitleId)\n\n    // 禁用所有现有字幕轨道\n    const tracks = video.textTracks\n    for (let i = 0; i < tracks.length; i++) {\n      tracks[i].mode = 'disabled'\n    }\n\n    if (subtitleId === 'none') {\n      setSelectedSubtitle('none')\n      return\n    }\n\n    const selectedTrack = subtitleTracks.find(track => track.id === subtitleId)\n    if (selectedTrack && selectedTrack.src) {\n      setSelectedSubtitle(subtitleId)\n\n      // 清除现有的字幕轨道元素\n      const existingTracks = video.querySelectorAll('track')\n      existingTracks.forEach(track => track.remove())\n\n      // 创建新的字幕轨道元素\n      const trackElement = document.createElement('track')\n      trackElement.kind = 'subtitles'\n      trackElement.src = selectedTrack.src\n      trackElement.srcLang = 'zh'\n      trackElement.label = selectedTrack.label\n      trackElement.default = true\n\n      // 添加到video元素\n      video.appendChild(trackElement)\n\n      // 启用字幕显示\n      trackElement.addEventListener('load', () => {\n        console.log('字幕文件加载完成:', selectedTrack.src)\n        trackElement.track.mode = 'showing'\n        console.log('字幕轨道已启用:', trackElement.track.label)\n        \n        // 检查字幕内容\n        if (trackElement.track.cues && trackElement.track.cues.length > 0) {\n          console.log('字幕提示数量:', trackElement.track.cues.length)\n          console.log('第一个字幕内容:', trackElement.track.cues[0].text)\n        }\n      })\n\n      trackElement.addEventListener('error', (e) => {\n        console.error('字幕文件加载失败:', e)\n      })\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-3xl font-bold text-white mb-8\">简化字幕测试</h1>\n        \n        <div className=\"bg-black rounded-lg overflow-hidden mb-6\">\n          <video\n            ref={videoRef}\n            className=\"w-full h-auto\"\n            crossOrigin=\"anonymous\"\n            controls\n            onLoadedMetadata={() => {\n              console.log('视频元数据加载完成')\n              // 自动加载默认字幕\n              setTimeout(() => {\n                handleSubtitleChange('zh')\n              }, 1000)\n            }}\n          >\n            <source src=\"/uploads/movies/movie_1755665391178.mp4\" type=\"video/mp4\" />\n            您的浏览器不支持视频播放。\n          </video>\n        </div>\n\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-white mb-4\">字幕选择</h2>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6\">\n            {subtitleTracks.map((track) => (\n              <button\n                key={track.id}\n                onClick={() => handleSubtitleChange(track.id)}\n                className={`p-3 rounded-lg text-sm font-medium transition-colors ${\n                  selectedSubtitle === track.id\n                    ? 'bg-red-600 text-white'\n                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'\n                }`}\n              >\n                {track.label}\n              </button>\n            ))}\n          </div>\n\n          <div className=\"text-gray-300\">\n            <h3 className=\"text-lg font-semibold mb-2\">测试说明</h3>\n            <ul className=\"text-sm space-y-1\">\n              <li>• 选择\"中韩字幕\"应该看到中文和韩文两行</li>\n              <li>• 选择\"中英字幕\"应该看到中文和英文两行</li>\n              <li>• 打开浏览器开发者工具查看控制台日志</li>\n              <li>• 如果只看到中文，说明双语字幕显示有问题</li>\n            </ul>\n          </div>\n\n          <div className=\"mt-4 p-4 bg-gray-700 rounded\">\n            <h4 className=\"text-white font-semibold mb-2\">当前状态</h4>\n            <p className=\"text-gray-300\">选中字幕: {subtitleTracks.find(t => t.id === selectedSubtitle)?.label}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;QAwIwB;;IAvI9C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,KAAK;QACP;KACD;IAED,MAAM,uBAAuB,CAAC;QAC5B,MAAM,QAAQ,SAAS,OAAO;QAC9B,IAAI,CAAC,OAAO;QAEZ,QAAQ,GAAG,CAAC,SAAS;QAErB,aAAa;QACb,MAAM,SAAS,MAAM,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;QACnB;QAEA,IAAI,eAAe,QAAQ;YACzB,oBAAoB;YACpB;QACF;QAEA,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChE,IAAI,iBAAiB,cAAc,GAAG,EAAE;YACtC,oBAAoB;YAEpB,cAAc;YACd,MAAM,iBAAiB,MAAM,gBAAgB,CAAC;YAC9C,eAAe,OAAO,CAAC,CAAA,QAAS,MAAM,MAAM;YAE5C,aAAa;YACb,MAAM,eAAe,SAAS,aAAa,CAAC;YAC5C,aAAa,IAAI,GAAG;YACpB,aAAa,GAAG,GAAG,cAAc,GAAG;YACpC,aAAa,OAAO,GAAG;YACvB,aAAa,KAAK,GAAG,cAAc,KAAK;YACxC,aAAa,OAAO,GAAG;YAEvB,aAAa;YACb,MAAM,WAAW,CAAC;YAElB,SAAS;YACT,aAAa,gBAAgB,CAAC,QAAQ;gBACpC,QAAQ,GAAG,CAAC,aAAa,cAAc,GAAG;gBAC1C,aAAa,KAAK,CAAC,IAAI,GAAG;gBAC1B,QAAQ,GAAG,CAAC,YAAY,aAAa,KAAK,CAAC,KAAK;gBAEhD,SAAS;gBACT,IAAI,aAAa,KAAK,CAAC,IAAI,IAAI,aAAa,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;oBACjE,QAAQ,GAAG,CAAC,WAAW,aAAa,KAAK,CAAC,IAAI,CAAC,MAAM;oBACrD,QAAQ,GAAG,CAAC,YAAY,aAAa,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;gBACzD;YACF;YAEA,aAAa,gBAAgB,CAAC,SAAS,CAAC;gBACtC,QAAQ,KAAK,CAAC,aAAa;YAC7B;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAqC;;;;;;8BAEnD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,KAAK;wBACL,WAAU;wBACV,aAAY;wBACZ,QAAQ;wBACR,kBAAkB;4BAChB,QAAQ,GAAG,CAAC;4BACZ,WAAW;4BACX,WAAW;gCACT,qBAAqB;4BACvB,GAAG;wBACL;;0CAEA,6LAAC;gCAAO,KAAI;gCAA0C,MAAK;;;;;;4BAAc;;;;;;;;;;;;8BAK7E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;oCAEC,SAAS,IAAM,qBAAqB,MAAM,EAAE;oCAC5C,WAAW,AAAC,wDAIX,OAHC,qBAAqB,MAAM,EAAE,GACzB,0BACA;8CAGL,MAAM,KAAK;mCARP,MAAM,EAAE;;;;;;;;;;sCAanB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,6LAAC;oCAAE,WAAU;;wCAAgB;yCAAO,uBAAA,eAAe,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,+BAAlC,2CAAA,qBAAqD,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1G;GA9IwB;KAAA", "debugId": null}}]}